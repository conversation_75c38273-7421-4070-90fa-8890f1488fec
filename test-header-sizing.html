<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Header Sizing Test - Sisters for Good</title>
  
  <!-- Font Loading Optimizer (prevents header size jumping) -->
  <script src="js/font-loading-optimizer.js"></script>
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">
  
  <style>
    /* Test-specific styles */
    .test-section {
      margin: 2rem 0;
      padding: 2rem;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      background: white;
    }
    
    .test-header {
      background: #f3f4f6;
      padding: 1rem;
      border-radius: 4px;
      margin: 1rem 0;
    }
    
    .size-indicator {
      position: absolute;
      right: 10px;
      top: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 10px;
      font-family: monospace;
    }
    
    .test-container {
      position: relative;
    }
    
    .loading-simulation {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 2s infinite;
    }
    
    @keyframes loading {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }
    
    .status-good { color: #16a34a; }
    .status-bad { color: #dc2626; }
    .status-warning { color: #d97706; }
  </style>
</head>
<body>
  <div class="container mx-auto px-4 py-8 max-w-6xl">
    <h1 class="text-4xl font-bold text-center mb-8 text-gray-900">Header Sizing Test</h1>
    
    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Test Overview</h2>
      <p class="mb-4">This page tests the header sizing fix to ensure headers maintain consistent sizing during page load without visual "jumping" or "flash" effects.</p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="bg-green-50 border border-green-200 p-4 rounded">
          <h3 class="font-bold text-green-800">✅ Fixed Issues</h3>
          <ul class="text-sm text-green-700 mt-2">
            <li>• Font loading optimization</li>
            <li>• Consistent header sizing</li>
            <li>• Eliminated layout shifts</li>
            <li>• Smooth font transitions</li>
          </ul>
        </div>
        
        <div class="bg-blue-50 border border-blue-200 p-4 rounded">
          <h3 class="font-bold text-blue-800">🔧 Implementation</h3>
          <ul class="text-sm text-blue-700 mt-2">
            <li>• Font Loading Optimizer JS</li>
            <li>• CSS clamp() sizing</li>
            <li>• min-height properties</li>
            <li>• font-display: swap</li>
          </ul>
        </div>
        
        <div class="bg-purple-50 border border-purple-200 p-4 rounded">
          <h3 class="font-bold text-purple-800">📊 Testing</h3>
          <ul class="text-sm text-purple-700 mt-2">
            <li>• Cross-browser testing</li>
            <li>• Mobile device testing</li>
            <li>• Network speed testing</li>
            <li>• Font loading simulation</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Header Size Consistency Test</h2>
      <p class="mb-4">These headers should maintain consistent sizing from initial load:</p>
      
      <div class="space-y-4">
        <div class="test-container">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900">H1 Header - Main Page Title</h1>
          <div class="size-indicator" id="h1-size">Measuring...</div>
        </div>
        
        <div class="test-container">
          <h2 class="text-3xl font-bold text-gray-800">H2 Header - Section Title</h2>
          <div class="size-indicator" id="h2-size">Measuring...</div>
        </div>
        
        <div class="test-container">
          <h3 class="text-2xl font-bold text-gray-700">H3 Header - Subsection Title</h3>
          <div class="size-indicator" id="h3-size">Measuring...</div>
        </div>
        
        <div class="test-container">
          <h4 class="text-xl font-bold text-gray-600">H4 Header - Minor Section</h4>
          <div class="size-indicator" id="h4-size">Measuring...</div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Navigation Header Test</h2>
      <p class="mb-4">Simulating the actual website header:</p>
      
      <header class="bg-white shadow-md p-4 rounded-lg">
        <div class="flex justify-between items-center">
          <div class="test-container">
            <h1 class="text-xl sm:text-2xl font-bold text-pink-600">Sisters for Good</h1>
            <div class="size-indicator" id="nav-size">Measuring...</div>
          </div>
          <nav class="hidden md:flex space-x-6">
            <a href="#" class="text-gray-700 hover:text-pink-600 transition-colors">Home</a>
            <a href="#" class="text-gray-700 hover:text-pink-600 transition-colors">About</a>
            <a href="#" class="text-gray-700 hover:text-pink-600 transition-colors">Programs</a>
          </nav>
        </div>
      </header>
    </div>

    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Hero Section Test</h2>
      <p class="mb-4">Testing hero section headers that commonly experience sizing issues:</p>
      
      <div class="bg-gradient-to-r from-pink-600 to-purple-600 text-white p-8 rounded-lg">
        <div class="test-container">
          <h1 class="text-4xl md:text-5xl font-bold mb-4">Hero Section Title</h1>
          <div class="size-indicator" id="hero-size">Measuring...</div>
        </div>
        <p class="text-xl max-w-3xl">This is a hero section subtitle that should maintain consistent sizing.</p>
      </div>
    </div>

    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Font Loading Status</h2>
      <div id="font-status" class="space-y-2">
        <p>Checking font loading status...</p>
      </div>
    </div>

    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Performance Metrics</h2>
      <div id="performance-metrics" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Layout Stability</h3>
          <div id="cls-score">Measuring CLS...</div>
        </div>
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Font Load Time</h3>
          <div id="font-load-time">Measuring...</div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Manual Testing Instructions</h2>
      <div class="bg-yellow-50 border border-yellow-200 p-4 rounded">
        <h3 class="font-bold text-yellow-800 mb-2">How to Test:</h3>
        <ol class="list-decimal list-inside space-y-2 text-yellow-700">
          <li><strong>Refresh Test:</strong> Refresh this page multiple times and watch for header size changes</li>
          <li><strong>Network Throttling:</strong> Use browser dev tools to simulate slow network speeds</li>
          <li><strong>Cache Clearing:</strong> Clear browser cache and reload to test first-time visits</li>
          <li><strong>Mobile Testing:</strong> Test on mobile devices with varying network speeds</li>
          <li><strong>Font Blocking:</strong> Temporarily block font loading to test fallback behavior</li>
        </ol>
      </div>
    </div>

    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Test Results</h2>
      <div id="test-results" class="space-y-4">
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Automated Test Results</h3>
          <div id="automated-results">Running tests...</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Test script to measure header sizes and detect changes
    class HeaderSizeTest {
      constructor() {
        this.measurements = new Map();
        this.startTime = performance.now();
        this.init();
      }

      init() {
        this.measureHeaders();
        this.checkFontStatus();
        this.measurePerformance();
        this.runAutomatedTests();
        
        // Re-measure periodically to detect changes
        setInterval(() => this.measureHeaders(), 100);
      }

      measureHeaders() {
        const headers = [
          { selector: 'h1', id: 'h1-size' },
          { selector: 'h2', id: 'h2-size' },
          { selector: 'h3', id: 'h3-size' },
          { selector: 'h4', id: 'h4-size' },
          { selector: 'header h1', id: 'nav-size' },
          { selector: '.bg-gradient-to-r h1', id: 'hero-size' }
        ];

        headers.forEach(({ selector, id }) => {
          const element = document.querySelector(selector);
          const indicator = document.getElementById(id);
          
          if (element && indicator) {
            const rect = element.getBoundingClientRect();
            const size = `${Math.round(rect.width)}×${Math.round(rect.height)}`;
            
            // Check for size changes
            const previousSize = this.measurements.get(id);
            if (previousSize && previousSize !== size) {
              indicator.textContent = `${size} (CHANGED!)`;
              indicator.style.background = 'rgba(220, 38, 38, 0.9)';
              console.warn(`Header size changed for ${selector}: ${previousSize} → ${size}`);
            } else {
              indicator.textContent = size;
              indicator.style.background = 'rgba(0, 0, 0, 0.7)';
            }
            
            this.measurements.set(id, size);
          }
        });
      }

      checkFontStatus() {
        const statusDiv = document.getElementById('font-status');
        
        if (window.fontLoadingOptimizer) {
          const isLoaded = window.fontLoadingOptimizer.areFontsLoaded();
          statusDiv.innerHTML = `
            <p class="${isLoaded ? 'status-good' : 'status-warning'}">
              Font Loading Status: ${isLoaded ? '✅ Loaded' : '⏳ Loading...'}
            </p>
            <p>Font Loading Optimizer: ✅ Active</p>
          `;
        } else {
          statusDiv.innerHTML = `
            <p class="status-bad">❌ Font Loading Optimizer not found</p>
          `;
        }

        // Check if fonts are actually loaded
        if ('fonts' in document) {
          document.fonts.ready.then(() => {
            const loadTime = performance.now() - this.startTime;
            document.getElementById('font-load-time').innerHTML = `
              <span class="status-good">✅ ${Math.round(loadTime)}ms</span>
            `;
          });
        }
      }

      measurePerformance() {
        // Measure Cumulative Layout Shift (CLS)
        if ('PerformanceObserver' in window) {
          const observer = new PerformanceObserver((list) => {
            let clsScore = 0;
            for (const entry of list.getEntries()) {
              if (!entry.hadRecentInput) {
                clsScore += entry.value;
              }
            }
            
            const clsDiv = document.getElementById('cls-score');
            if (clsScore === 0) {
              clsDiv.innerHTML = '<span class="status-good">✅ 0 (Perfect)</span>';
            } else if (clsScore < 0.1) {
              clsDiv.innerHTML = `<span class="status-good">✅ ${clsScore.toFixed(4)} (Good)</span>`;
            } else if (clsScore < 0.25) {
              clsDiv.innerHTML = `<span class="status-warning">⚠️ ${clsScore.toFixed(4)} (Needs Improvement)</span>`;
            } else {
              clsDiv.innerHTML = `<span class="status-bad">❌ ${clsScore.toFixed(4)} (Poor)</span>`;
            }
          });
          
          observer.observe({ entryTypes: ['layout-shift'] });
        }
      }

      runAutomatedTests() {
        const results = [];
        
        // Test 1: Check if font loading optimizer is present
        if (window.fontLoadingOptimizer) {
          results.push('✅ Font Loading Optimizer is active');
        } else {
          results.push('❌ Font Loading Optimizer not found');
        }
        
        // Test 2: Check if headers have min-height
        const headers = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        let headersWithMinHeight = 0;
        headers.forEach(header => {
          const style = window.getComputedStyle(header);
          if (style.minHeight && style.minHeight !== 'auto' && style.minHeight !== '0px') {
            headersWithMinHeight++;
          }
        });
        
        if (headersWithMinHeight > 0) {
          results.push(`✅ ${headersWithMinHeight}/${headers.length} headers have min-height set`);
        } else {
          results.push('⚠️ No headers have min-height set');
        }
        
        // Test 3: Check font-display property
        const hasSwapFont = Array.from(document.styleSheets).some(sheet => {
          try {
            return Array.from(sheet.cssRules).some(rule => 
              rule.cssText && rule.cssText.includes('font-display: swap')
            );
          } catch (e) {
            return false;
          }
        });
        
        if (hasSwapFont) {
          results.push('✅ font-display: swap detected');
        } else {
          results.push('⚠️ font-display: swap not detected');
        }
        
        // Test 4: Check for clamp() usage
        const hasClampSizing = Array.from(document.styleSheets).some(sheet => {
          try {
            return Array.from(sheet.cssRules).some(rule => 
              rule.cssText && rule.cssText.includes('clamp(')
            );
          } catch (e) {
            return false;
          }
        });
        
        if (hasClampSizing) {
          results.push('✅ clamp() responsive sizing detected');
        } else {
          results.push('⚠️ clamp() responsive sizing not detected');
        }
        
        document.getElementById('automated-results').innerHTML = results.join('<br>');
      }
    }

    // Initialize test when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
      new HeaderSizeTest();
    });

    // Listen for font loading events
    document.addEventListener('fontsLoaded', () => {
      console.log('✅ Fonts loaded successfully');
    });
  </script>
</body>
</html>
