[Title] Sisters For Good 🌸

[Link] 🌐 Live Site: https://sistersforgood.org

[Section] Project Overview
– Official website for Sisters For Good  
– Mobile‑first, accessible design  
– Sections: programs, services, events  
– Crypto‑friendly donations  
– Custom JSON‑powered chatbot

[Section] Tech Stack
• HTML5 + CSS3  
• JavaScript (vanilla + JSON Q&A)  
• GitHub Pages hosting  
• GoDaddy DNS for custom domain  
• Responsive Flex/Grid layouts

[Section] New Features
✔️ Live custom domain (`sistersforgood.org`)  
✔️ Correct A‑records for GitHub Pages  
✔️ Removed “coming soon” placeholder  
✔️ Enhanced chatbot with JSON fallback  
✔️ Semantic HTML & SEO improvements  
✔️ Continuous deployment from `main`  
✔️ Optimized image loading

[Section] File Structure
• `/index.html` — Homepage  
• `/chatbot.js` — Chatbot logic  
• `/data/faq.json` — Q&A knowledge base  
• `/images/` — Visual assets  
• `/styles/` — CSS stylesheets  
• `favicon.ico` — Site icon

[Section] Domain & Hosting
• Domain: `sistersforgood.org`  
• Registrar: GoDaddy  
• Hosting: GitHub Pages  
• DNS A‑records:  
  - ***************  
  - ***************  
  - ***************  
  - ***************

[Section] How It Works
1. GitHub Pages builds from `main`.  
2. Custom domain points to GitHub IPs.  
3. Pushes to `main` auto‑deploy.  
4. Chatbot reads from `faq.json` for dynamic replies.

[Section] Contributing
1. Fork the repo  
2. Create a feature branch  
3. Submit a PR with clear notes

[Section] Contact Us
• Email: <EMAIL>  
• Phone: (610) 288‑0343  
• Location: Pennsylvania, USA

[Section] Support Us
We accept **crypto**, **PayPal**, and traditional donations. Every bit helps!

[Section] License
MIT License — free to use, modify, and share. Please credit us.

[Quote] “Empowered women empower communities.” – Sisters For Good
