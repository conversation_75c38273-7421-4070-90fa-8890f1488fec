/**
 * Font Loading Optimizer
 * Prevents header size jumping and layout shifts during font loading
 */

class FontLoadingOptimizer {
  constructor() {
    this.fontsToLoad = [
      {
        family: 'Poppins',
        weights: ['400', '500', '600', '700'],
        display: 'swap'
      },
      {
        family: 'Roboto',
        weights: ['400', '500'],
        display: 'swap'
      }
    ];
    this.loadingClass = 'fonts-loading';
    this.loadedClass = 'fonts-loaded';
    this.init();
  }

  init() {
    // Add loading class immediately
    document.documentElement.classList.add(this.loadingClass);
    
    // Preload critical fonts
    this.preloadFonts();
    
    // Use Font Loading API if available
    if ('fonts' in document) {
      this.useFontLoadingAPI();
    } else {
      // Fallback for older browsers
      this.useFallbackMethod();
    }
    
    // Prevent layout shifts during loading
    this.preventLayoutShifts();
  }

  preloadFonts() {
    const head = document.head;
    
    this.fontsToLoad.forEach(font => {
      font.weights.forEach(weight => {
        // Create preload link for each font weight
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        link.href = `https://fonts.gstatic.com/s/${font.family.toLowerCase()}/v20/${font.family.toLowerCase()}-${weight}.woff2`;
        
        head.appendChild(link);
      });
    });
  }

  useFontLoadingAPI() {
    const fontPromises = [];
    
    this.fontsToLoad.forEach(font => {
      font.weights.forEach(weight => {
        const fontFace = new FontFace(
          font.family,
          `url(https://fonts.gstatic.com/s/${font.family.toLowerCase()}/v20/${font.family.toLowerCase()}-${weight}.woff2) format('woff2')`,
          {
            weight: weight,
            display: font.display
          }
        );
        
        fontPromises.push(fontFace.load());
        document.fonts.add(fontFace);
      });
    });

    // Wait for all fonts to load
    Promise.all(fontPromises)
      .then(() => {
        this.onFontsLoaded();
      })
      .catch((error) => {
        console.warn('Font loading failed:', error);
        // Still mark as loaded to prevent indefinite loading state
        setTimeout(() => this.onFontsLoaded(), 3000);
      });

    // Fallback timeout
    setTimeout(() => {
      if (document.documentElement.classList.contains(this.loadingClass)) {
        this.onFontsLoaded();
      }
    }, 5000);
  }

  useFallbackMethod() {
    // For browsers without Font Loading API
    const testString = 'BESbswy';
    const testSize = '72px';
    const fallbackFont = 'monospace';
    
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.fontSize = testSize;
    container.style.fontFamily = fallbackFont;
    container.innerHTML = testString;
    document.body.appendChild(container);
    
    const fallbackWidth = container.offsetWidth;
    
    // Test each font
    let fontsLoaded = 0;
    const totalFonts = this.fontsToLoad.length;
    
    this.fontsToLoad.forEach(font => {
      const testElement = document.createElement('div');
      testElement.style.position = 'absolute';
      testElement.style.left = '-9999px';
      testElement.style.top = '-9999px';
      testElement.style.fontSize = testSize;
      testElement.style.fontFamily = `${font.family}, ${fallbackFont}`;
      testElement.innerHTML = testString;
      document.body.appendChild(testElement);
      
      const checkFont = () => {
        if (testElement.offsetWidth !== fallbackWidth) {
          fontsLoaded++;
          document.body.removeChild(testElement);
          
          if (fontsLoaded === totalFonts) {
            document.body.removeChild(container);
            this.onFontsLoaded();
          }
        } else {
          setTimeout(checkFont, 100);
        }
      };
      
      checkFont();
    });
    
    // Fallback timeout
    setTimeout(() => {
      if (document.documentElement.classList.contains(this.loadingClass)) {
        this.onFontsLoaded();
      }
    }, 5000);
  }

  onFontsLoaded() {
    document.documentElement.classList.remove(this.loadingClass);
    document.documentElement.classList.add(this.loadedClass);
    
    // Trigger a reflow to ensure proper sizing
    this.triggerReflow();
    
    // Dispatch custom event
    const event = new CustomEvent('fontsLoaded', {
      detail: { optimizer: this }
    });
    document.dispatchEvent(event);
    
    console.log('✅ Fonts loaded successfully - layout shifts prevented');
  }

  preventLayoutShifts() {
    // Add CSS to prevent layout shifts
    const style = document.createElement('style');
    style.textContent = `
      /* Prevent layout shifts during font loading */
      .${this.loadingClass} h1,
      .${this.loadingClass} h2,
      .${this.loadingClass} h3,
      .${this.loadingClass} h4,
      .${this.loadingClass} h5,
      .${this.loadingClass} h6 {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif !important;
        font-size-adjust: 0.5;
        text-rendering: optimizeSpeed;
      }
      
      .${this.loadingClass} body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif !important;
      }
      
      /* Smooth transition when fonts load */
      .${this.loadedClass} h1,
      .${this.loadedClass} h2,
      .${this.loadedClass} h3,
      .${this.loadedClass} h4,
      .${this.loadedClass} h5,
      .${this.loadedClass} h6,
      .${this.loadedClass} body {
        transition: font-family 0.1s ease-out;
      }
      
      /* Ensure headers maintain size during loading */
      .${this.loadingClass} header h1 {
        min-height: 1.5em;
        line-height: 1.2;
      }
      
      .${this.loadingClass} .text-4xl,
      .${this.loadingClass} .text-5xl {
        min-height: 1.2em;
        line-height: 1.1;
      }
      
      .${this.loadingClass} .text-xl,
      .${this.loadingClass} .text-2xl,
      .${this.loadingClass} .text-3xl {
        min-height: 1.2em;
      }
    `;
    
    document.head.appendChild(style);
  }

  triggerReflow() {
    // Force a reflow to ensure proper sizing after font loading
    const headers = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headers.forEach(header => {
      const display = header.style.display;
      header.style.display = 'none';
      header.offsetHeight; // Trigger reflow
      header.style.display = display;
    });
  }

  // Public method to check if fonts are loaded
  areFontsLoaded() {
    return document.documentElement.classList.contains(this.loadedClass);
  }

  // Public method to manually trigger font loading check
  checkFontLoading() {
    if ('fonts' in document) {
      return document.fonts.ready.then(() => {
        if (!this.areFontsLoaded()) {
          this.onFontsLoaded();
        }
      });
    }
    return Promise.resolve();
  }
}

// Initialize font loading optimizer immediately
const fontOptimizer = new FontLoadingOptimizer();

// Make it globally available
window.fontLoadingOptimizer = fontOptimizer;

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FontLoadingOptimizer;
}
