<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Contact Sisters for Good. Reach out to us with questions, volunteer inquiries, or partnership opportunities.">
  <title>Sisters for Good | Contact Us</title>

  <!-- Favicon -->
  <link rel="icon" href="favicon.ico" type="image/x-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#fc5c7d',
            secondary: '#6a82fb',
            accent: '#ffee02'
          },
          fontFamily: {
            sans: ['Poppins', 'sans-serif'],
            body: ['Roboto', 'sans-serif']
          }
        }
      }
    }
  </script>
</head>
<body>

<!-- Header Component -->
<header class="bg-white shadow-md sticky top-0 z-50">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <a href="index.html" class="flex items-center">
        <h1 class="text-xl sm:text-2xl font-bold text-pink-600">Sisters for Good</h1>
      </a>

      <!-- Desktop Navigation (hidden on mobile) -->
      <nav class="hidden md:flex space-x-6">
        <a href="index.html" class="nav-link hover:text-pink-600 transition-colors font-medium">Home</a>
        <a href="about.html" class="nav-link hover:text-pink-600 transition-colors">About Us</a>
        <a href="programs.html" class="nav-link hover:text-pink-600 transition-colors">Programs</a>
        <a href="events.html" class="nav-link hover:text-pink-600 transition-colors">Events</a>
        <a href="health.html" class="nav-link hover:text-pink-600 transition-colors">Health Resources</a>
        <a href="donate.html" class="nav-link hover:text-pink-600 transition-colors">Donate</a>
        <a href="contact.html" class="nav-link text-pink-600 font-semibold">Contact</a>
      </nav>

      <!-- Mobile menu button -->
      <button id="mobileMenuBtn" class="md:hidden flex items-center p-3 rounded-md hover:bg-gray-100 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation (hidden by default) -->
    <div id="mobileMenu" class="md:hidden hidden mt-4 pb-4 border-t border-gray-200 pt-3">
      <nav class="flex flex-col space-y-4">
        <a href="index.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Home</a>
        <a href="about.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">About Us</a>
        <a href="programs.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Programs</a>
        <a href="events.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Events</a>
        <a href="health.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Health Resources</a>
        <a href="donate.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Donate</a>
        <a href="contact.html" class="nav-link text-center py-2 bg-pink-50 text-pink-600 font-medium rounded-md">Contact</a>
      </nav>
    </div>
  </div>
</header>

<!-- Page Header -->
<section class="bg-gradient-to-r from-slate-700 to-teal-600 text-white py-16 relative overflow-hidden">
  <div class="absolute inset-0 bg-black opacity-10"></div>
  <div class="container mx-auto px-4 text-center relative z-10">
    <h1 class="text-4xl md:text-5xl font-bold mb-4 drop-shadow-lg" data-aos="fade-up">Contact Us</h1>
    <p class="text-xl max-w-3xl mx-auto leading-relaxed drop-shadow-md" data-aos="fade-up" data-aos-delay="100">We'd love to hear from you! Reach out with questions, volunteer inquiries, or partnership opportunities.</p>
    <div class="mt-8 flex flex-wrap justify-center items-center gap-4 md:gap-6" data-aos="fade-up" data-aos-delay="200">
      <a href="tel:************" class="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2 hover:bg-white/30 transition-colors">
        <span class="text-lg">📞</span>
        <span class="text-sm font-medium">(*************</span>
      </a>
      <a href="mailto:<EMAIL>" class="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2 hover:bg-white/30 transition-colors">
        <span class="text-lg">📧</span>
        <span class="text-sm font-medium hidden sm:inline"><EMAIL></span>
        <span class="text-sm font-medium sm:hidden">Email Us</span>
      </a>
    </div>
  </div>
</section>

<!-- Contact Information Section -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Contact Information -->
      <div data-aos="fade-right">
        <h2 class="text-3xl font-bold mb-6">Get In Touch</h2>
        <div class="mb-8">
          <h3 class="text-xl font-bold mb-3">Our Location</h3>
          <p class="flex items-start mb-2">
            <span class="text-primary mr-3">📍</span>
            <span>247 East High St<br>Pottstown, PA 19464</span>
          </p>
        </div>
        <div class="mb-8">
          <h3 class="text-xl font-bold mb-3">Contact Information</h3>
          <p class="flex items-center mb-2">
            <span class="text-primary mr-3">📞</span>
            <span>(*************</span>
          </p>
          <p class="flex items-center mb-2">
            <span class="text-primary mr-3">📧</span>
            <a href="mailto:<EMAIL>" class="hover:text-primary transition-colors"><EMAIL></a>
          </p>
        </div>
        <div class="mb-8">
          <h3 class="text-xl font-bold mb-3">Hours of Operation</h3>
          <p class="mb-1">Monday - Friday: 9:00 AM - 5:00 PM</p>
          <p>Saturday - Sunday: Closed</p>
        </div>
        <div>
          <h3 class="text-xl font-bold mb-3">Connect With Us</h3>
          <div class="flex space-x-4">
            <a href="#" class="text-primary hover:text-secondary transition-colors">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
              </svg>
            </a>
            <a href="#" class="text-primary hover:text-secondary transition-colors">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
              </svg>
            </a>
            <a href="#" class="text-primary hover:text-secondary transition-colors">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Google Form -->
      <div data-aos="fade-left">
        <div id="google-form-container">
          <!-- Google Form will be loaded here -->
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Map Section -->
<section class="section bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Find Us</h2>
    </div>
    <div class="card overflow-hidden" data-aos="fade-up">
      <div class="aspect-w-16 aspect-h-9">
        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3053.7457396462394!2d-75.65773492346177!3d40.24553437146838!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c6a0f9d5e3d5a5%3A0x1c1c2c7a7e9c5a5a!2s247%20E%20High%20St%2C%20Pottstown%2C%20PA%2019464!5e0!3m2!1sen!2sus!4v1650000000000!5m2!1sen!2sus" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Frequently Asked Questions</h2>
    </div>
    <div class="max-w-3xl mx-auto">
      <div class="mb-6" data-aos="fade-up">
        <h3 class="text-xl font-bold mb-2">How can I volunteer with Sisters for Good?</h3>
        <p>You can volunteer by filling out the contact form above or sending an <NAME_EMAIL>. We have opportunities in event planning, mentorship, teaching, and administrative support.</p>
      </div>
      <div class="mb-6" data-aos="fade-up" data-aos-delay="100">
        <h3 class="text-xl font-bold mb-2">Do you offer internship opportunities?</h3>
        <p>Yes, we offer internship opportunities for college students and recent graduates. Please contact us with your resume and area of interest to learn about current openings.</p>
      </div>
      <div class="mb-6" data-aos="fade-up" data-aos-delay="200">
        <h3 class="text-xl font-bold mb-2">How can I partner with Sisters for Good?</h3>
        <p>We welcome partnerships with businesses, schools, and other organizations that share our mission. Please reach out through our contact form to discuss potential collaboration opportunities.</p>
      </div>
      <div data-aos="fade-up" data-aos-delay="300">
        <h3 class="text-xl font-bold mb-2">Can I schedule a visit to learn more about your programs?</h3>
        <p>Absolutely! We'd be happy to give you a tour of our facilities and provide more information about our programs. Please contact us to schedule a visit.</p>
      </div>
    </div>
  </div>
</section>

<!-- Footer Component -->
<footer class="bg-gray-800 text-white py-10">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Organization Info -->
      <div>
        <h3 class="text-xl font-bold mb-4">Sisters for Good</h3>
        <p class="mb-2">Empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach.</p>
        <p class="text-sm mt-4">&copy; 2025 Sisters for Good. All Rights Reserved.</p>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-xl font-bold mb-4">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="index.html" class="hover:text-pink-400 transition-colors">Home</a></li>
          <li><a href="about.html" class="hover:text-pink-400 transition-colors">About Us</a></li>
          <li><a href="programs.html" class="hover:text-pink-400 transition-colors">Programs</a></li>
          <li><a href="events.html" class="hover:text-pink-400 transition-colors">Events</a></li>
          <li><a href="health.html" class="hover:text-pink-400 transition-colors">Health Resources</a></li>
          <li><a href="donate.html" class="hover:text-pink-400 transition-colors">Donate</a></li>
          <li><a href="contact.html" class="hover:text-pink-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Information -->
      <div>
        <h3 class="text-xl font-bold mb-4">Contact Us</h3>
        <address class="not-italic">
          <p class="mb-2">📍 247 East High St, Pottstown, PA 19464</p>
          <p class="mb-2">📞 (*************</p>
          <p class="mb-2">📧 <a href="mailto:<EMAIL>" class="hover:text-pink-400 transition-colors"><EMAIL></a></p>
        </address>

        <!-- Social Media Links -->
        <div class="mt-4 flex space-x-4">
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Facebook</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Instagram</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772a4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Twitter</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>

<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>

<!-- Google Form is loaded via main.js -->

<!-- Initialize Scripts -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile menu
    initMobileMenu();

    // Set active nav link
    setActiveNavLink();
  });
</script>
</body>
</html>
