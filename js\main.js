/**
 * Sisters for Good - Main JavaScript
 * Contains shared functionality for all pages
 */

// Initialize on DOM content loaded
document.addEventListener("DOMContentLoaded", () => {
  // Initialize mobile menu
  initMobileMenu();

  // Initialize AOS animations if library is loaded
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });
  }

  // Initialize functionality

  // Set active navigation link
  setActiveNavLink();

  // Load dynamic content if elements exist
  loadInspirationalQuote();
  loadEmpowermentNews();
  loadNewsletterComponent();
  loadTestimonialsComponent();
  loadGoogleFormComponent();
});

/**
 * Mobile menu functionality
 */
function initMobileMenu() {
  const mobileMenuBtn = document.getElementById('mobileMenuBtn');
  const mobileMenu = document.getElementById('mobileMenu');

  if (mobileMenuBtn && mobileMenu) {
    // Remove any existing event listeners (in case of duplicates)
    mobileMenuBtn.removeEventListener('click', toggleMobileMenu);

    // Add the event listener
    mobileMenuBtn.addEventListener('click', toggleMobileMenu);

    // Add event listener to close menu when clicking outside
    document.addEventListener('click', function(event) {
      const isClickInsideMenu = mobileMenu.contains(event.target);
      const isClickOnMenuButton = mobileMenuBtn.contains(event.target);

      if (!isClickInsideMenu && !isClickOnMenuButton && !mobileMenu.classList.contains('hidden')) {
        mobileMenu.classList.add('hidden');
      }
    });

    // Log that the mobile menu has been initialized
    console.log('Mobile menu initialized');
  } else {
    console.error('Mobile menu elements not found');
  }
}

/**
 * Toggle mobile menu visibility
 */
function toggleMobileMenu(event) {
  if (event) {
    event.stopPropagation(); // Prevent the click from bubbling up
  }

  const mobileMenu = document.getElementById('mobileMenu');
  if (mobileMenu) {
    mobileMenu.classList.toggle('hidden');
    console.log('Mobile menu toggled, hidden:', mobileMenu.classList.contains('hidden'));
  }
}

/**
 * Fetch and display inspirational quotes
 */
function loadInspirationalQuote() {
  const quoteText = document.getElementById("quoteText");
  const quoteAuthor = document.getElementById("quoteAuthor");

  if (quoteText && quoteAuthor) {
    fetch("https://news-api-flask-tmit.onrender.com/api/inspiration")
      .then(res => res.json())
      .then(data => {
        quoteText.innerText = `"${data.quote}"`;
        quoteAuthor.innerText = `– ${data.author}`;
      })
      .catch(() => {
        quoteText.innerText = "Believe in yourself and all that you are. Know that there is something inside you that is greater than any obstacle.";
        quoteAuthor.innerText = "– Christian D. Larson";
      });
  }
}

/**
 * Fetch and display women's empowerment news
 */
function loadEmpowermentNews() {
  const newsList = document.getElementById("empowermentNewsList");

  if (newsList) {
    fetch("https://news-api-flask-tmit.onrender.com/api/womens-empowerment")
      .then(res => res.json())
      .then(data => {
        newsList.innerHTML = "";
        data.forEach(article => {
          const item = document.createElement("li");
          item.innerHTML = `<a href="${article.url}" target="_blank" class="news-link">${article.title}</a>`;
          newsList.appendChild(item);
        });
      })
      .catch(() => {
        newsList.innerHTML = "<li>Could not load empowerment news. Please check back later.</li>";
      });
  }
}

/**
 * Set active navigation link based on current page
 */
function setActiveNavLink() {
  const currentPage = window.location.pathname.split('/').pop() || 'index.html';
  const navLinks = document.querySelectorAll('.nav-link');

  navLinks.forEach(link => {
    const linkHref = link.getAttribute('href');
    if (linkHref === currentPage) {
      link.classList.add('active');
    }
  });
}

/**
 * Animate elements when they come into view
 */
function animateOnScroll() {
  const animatedElements = document.querySelectorAll('.animate-on-scroll');

  if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animated');
        }
      });
    }, { threshold: 0.1 });

    animatedElements.forEach(element => {
      observer.observe(element);
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    animatedElements.forEach(element => {
      element.classList.add('animated');
    });
  }
}

/**
 * Load newsletter component
 */
function loadNewsletterComponent() {
  const newsletterContainer = document.getElementById('newsletter-container');
  if (!newsletterContainer) return;

  // Fetch newsletter component
  fetch('components/newsletter.html')
    .then(response => response.text())
    .then(data => {
      newsletterContainer.innerHTML = data;

      // Load the newsletter script after the component is loaded
      const script = document.createElement('script');
      script.src = 'js/newsletter.js';
      document.body.appendChild(script);
    })
    .catch(error => {
      console.error('Error loading newsletter component:', error);
      newsletterContainer.innerHTML = '<p class="text-center">Failed to load newsletter component.</p>';
    });
}

/**
 * Load testimonials component
 */
function loadTestimonialsComponent() {
  const testimonialsContainer = document.getElementById('testimonials-container');
  if (!testimonialsContainer) return;

  // Fetch testimonials component
  fetch('components/testimonials.html')
    .then(response => response.text())
    .then(data => {
      testimonialsContainer.innerHTML = data;

      // Load the testimonials script after the component is loaded
      const script = document.createElement('script');
      script.src = 'js/testimonials.js';
      document.body.appendChild(script);
    })
    .catch(error => {
      console.error('Error loading testimonials component:', error);
      testimonialsContainer.innerHTML = '<p class="text-center">Failed to load testimonials component.</p>';
    });
}

/**
 * Animate elements when they come into view
 */
function animateOnScroll() {
  const animatedElements = document.querySelectorAll('.animate-on-scroll');

  if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animated');
        }
      });
    }, { threshold: 0.1 });

    animatedElements.forEach(element => {
      observer.observe(element);
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    animatedElements.forEach(element => {
      element.classList.add('animated');
    });
  }
}

/**
 * Load Google Form component
 */
function loadGoogleFormComponent() {
  const googleFormContainer = document.getElementById('google-form-container');
  if (!googleFormContainer) return;

  // Fetch Google Form component
  fetch('components/google-form.html')
    .then(response => response.text())
    .then(data => {
      googleFormContainer.innerHTML = data;
    })
    .catch(error => {
      console.error('Error loading Google Form component:', error);
      googleFormContainer.innerHTML = '<p class="text-center">Failed to load contact form. Please email us <NAME_EMAIL>.</p>';
    });
}