/**
 * Sisters for Good - Main JavaScript
 * Contains shared functionality for all pages
 */

// Initialize on DOM content loaded
document.addEventListener("DOMContentLoaded", () => {
  // Initialize mobile menu
  initMobileMenu();

  // Initialize AOS animations if library is loaded
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });
  }

  // Initialize theme system
  initThemeSystem();

  // Set active navigation link
  setActiveNavLink();

  // Load dynamic content if elements exist
  loadInspirationalQuote();
  loadEmpowermentNews();
  loadNewsletterComponent();
  loadTestimonialsComponent();
  loadGoogleFormComponent();
});

/**
 * Mobile menu functionality
 */
function initMobileMenu() {
  const mobileMenuBtn = document.getElementById('mobileMenuBtn');
  const mobileMenu = document.getElementById('mobileMenu');

  if (mobileMenuBtn && mobileMenu) {
    // Remove any existing event listeners (in case of duplicates)
    mobileMenuBtn.removeEventListener('click', toggleMobileMenu);

    // Add the event listener
    mobileMenuBtn.addEventListener('click', toggleMobileMenu);

    // Add event listener to close menu when clicking outside
    document.addEventListener('click', function(event) {
      const isClickInsideMenu = mobileMenu.contains(event.target);
      const isClickOnMenuButton = mobileMenuBtn.contains(event.target);

      if (!isClickInsideMenu && !isClickOnMenuButton && !mobileMenu.classList.contains('hidden')) {
        mobileMenu.classList.add('hidden');
      }
    });

    // Log that the mobile menu has been initialized
    console.log('Mobile menu initialized');
  } else {
    console.error('Mobile menu elements not found');
  }
}

/**
 * Toggle mobile menu visibility
 */
function toggleMobileMenu(event) {
  if (event) {
    event.stopPropagation(); // Prevent the click from bubbling up
  }

  const mobileMenu = document.getElementById('mobileMenu');
  if (mobileMenu) {
    mobileMenu.classList.toggle('hidden');
    console.log('Mobile menu toggled, hidden:', mobileMenu.classList.contains('hidden'));
  }
}

/**
 * Fetch and display inspirational quotes
 */
function loadInspirationalQuote() {
  const quoteText = document.getElementById("quoteText");
  const quoteAuthor = document.getElementById("quoteAuthor");

  if (quoteText && quoteAuthor) {
    fetch("https://news-api-flask-tmit.onrender.com/api/inspiration")
      .then(res => res.json())
      .then(data => {
        quoteText.innerText = `"${data.quote}"`;
        quoteAuthor.innerText = `– ${data.author}`;
      })
      .catch(() => {
        quoteText.innerText = "Believe in yourself and all that you are. Know that there is something inside you that is greater than any obstacle.";
        quoteAuthor.innerText = "– Christian D. Larson";
      });
  }
}

/**
 * Fetch and display women's empowerment news
 */
function loadEmpowermentNews() {
  const newsList = document.getElementById("empowermentNewsList");

  if (newsList) {
    fetch("https://news-api-flask-tmit.onrender.com/api/womens-empowerment")
      .then(res => res.json())
      .then(data => {
        newsList.innerHTML = "";
        data.forEach(article => {
          const item = document.createElement("li");
          item.innerHTML = `<a href="${article.url}" target="_blank" class="news-link">${article.title}</a>`;
          newsList.appendChild(item);
        });
      })
      .catch(() => {
        newsList.innerHTML = "<li>Could not load empowerment news. Please check back later.</li>";
      });
  }
}

/**
 * Initialize theme system
 */
function initThemeSystem() {
  // Load saved theme or default to light
  const savedTheme = localStorage.getItem('sisters-for-good-theme');
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const currentTheme = savedTheme || (prefersDark ? 'dark' : 'light');

  // Apply theme immediately to prevent flash
  document.documentElement.setAttribute('data-theme', currentTheme);

  // Create and add theme toggle button
  createThemeToggle();
}

/**
 * Create theme toggle button
 */
function createThemeToggle() {
  // Check if toggle already exists
  if (document.getElementById('theme-toggle')) return;

  const toggleButton = document.createElement('button');
  toggleButton.id = 'theme-toggle';
  toggleButton.className = 'theme-toggle hidden md:flex';
  toggleButton.setAttribute('aria-label', 'Toggle dark mode');
  toggleButton.setAttribute('title', 'Toggle dark/light theme');

  toggleButton.innerHTML = `
    <svg class="sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
    </svg>
    <svg class="moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
    </svg>
  `;

  // Add click event listener
  toggleButton.addEventListener('click', toggleTheme);

  // Find the header navigation and add the button
  const headerNav = document.querySelector('header .flex.justify-between.items-center');
  if (headerNav) {
    // Insert before the mobile menu button
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    if (mobileMenuBtn) {
      headerNav.insertBefore(toggleButton, mobileMenuBtn);
    } else {
      headerNav.appendChild(toggleButton);
    }
  }
}

/**
 * Toggle between light and dark themes
 */
function toggleTheme() {
  const currentTheme = document.documentElement.getAttribute('data-theme');
  const newTheme = currentTheme === 'light' ? 'dark' : 'light';

  // Apply new theme
  document.documentElement.setAttribute('data-theme', newTheme);

  // Save to localStorage
  localStorage.setItem('sisters-for-good-theme', newTheme);

  // Update meta theme-color for mobile browsers
  updateMetaThemeColor(newTheme);

  // Dispatch custom event
  window.dispatchEvent(new CustomEvent('themeChanged', {
    detail: { theme: newTheme }
  }));
}

/**
 * Update meta theme color for mobile browsers
 */
function updateMetaThemeColor(theme) {
  let metaThemeColor = document.querySelector('meta[name="theme-color"]');

  if (!metaThemeColor) {
    metaThemeColor = document.createElement('meta');
    metaThemeColor.name = 'theme-color';
    document.head.appendChild(metaThemeColor);
  }

  const themeColor = theme === 'dark' ? '#0f172a' : '#ffffff';
  metaThemeColor.content = themeColor;
}

/**
 * Set active navigation link based on current page
 */
function setActiveNavLink() {
  const currentPage = window.location.pathname.split('/').pop() || 'index.html';
  const navLinks = document.querySelectorAll('.nav-link');

  navLinks.forEach(link => {
    const linkHref = link.getAttribute('href');
    if (linkHref === currentPage) {
      link.classList.add('active');
    }
  });
}

/**
 * Animate elements when they come into view
 */
function animateOnScroll() {
  const animatedElements = document.querySelectorAll('.animate-on-scroll');

  if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animated');
        }
      });
    }, { threshold: 0.1 });

    animatedElements.forEach(element => {
      observer.observe(element);
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    animatedElements.forEach(element => {
      element.classList.add('animated');
    });
  }
}

/**
 * Load newsletter component
 */
function loadNewsletterComponent() {
  const newsletterContainer = document.getElementById('newsletter-container');
  if (!newsletterContainer) return;

  // Fetch newsletter component
  fetch('components/newsletter.html')
    .then(response => response.text())
    .then(data => {
      newsletterContainer.innerHTML = data;

      // Load the newsletter script after the component is loaded
      const script = document.createElement('script');
      script.src = 'js/newsletter.js';
      document.body.appendChild(script);
    })
    .catch(error => {
      console.error('Error loading newsletter component:', error);
      newsletterContainer.innerHTML = '<p class="text-center">Failed to load newsletter component.</p>';
    });
}

/**
 * Load testimonials component
 */
function loadTestimonialsComponent() {
  const testimonialsContainer = document.getElementById('testimonials-container');
  if (!testimonialsContainer) return;

  // Fetch testimonials component
  fetch('components/testimonials.html')
    .then(response => response.text())
    .then(data => {
      testimonialsContainer.innerHTML = data;

      // Load the testimonials script after the component is loaded
      const script = document.createElement('script');
      script.src = 'js/testimonials.js';
      document.body.appendChild(script);
    })
    .catch(error => {
      console.error('Error loading testimonials component:', error);
      testimonialsContainer.innerHTML = '<p class="text-center">Failed to load testimonials component.</p>';
    });
}

/**
 * Animate elements when they come into view
 */
function animateOnScroll() {
  const animatedElements = document.querySelectorAll('.animate-on-scroll');

  if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animated');
        }
      });
    }, { threshold: 0.1 });

    animatedElements.forEach(element => {
      observer.observe(element);
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    animatedElements.forEach(element => {
      element.classList.add('animated');
    });
  }
}

/**
 * Load Google Form component
 */
function loadGoogleFormComponent() {
  const googleFormContainer = document.getElementById('google-form-container');
  if (!googleFormContainer) return;

  // Fetch Google Form component
  fetch('components/google-form.html')
    .then(response => response.text())
    .then(data => {
      googleFormContainer.innerHTML = data;
    })
    .catch(error => {
      console.error('Error loading Google Form component:', error);
      googleFormContainer.innerHTML = '<p class="text-center">Failed to load contact form. Please email us <NAME_EMAIL>.</p>';
    });
}