<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Sisters for Good Health Resources - Access local healthcare services, support hotlines, and educational resources to keep our community strong and healthy.">
  <title>Health Resources | Sisters for Good</title>

  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-XXXXXXXXXX');
  </script>

  <!-- Favicon -->
  <link rel="icon" href="favicon.ico" type="image/x-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Alpine.js -->
  <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#fc5c7d',
            secondary: '#6a82fb',
            accent: '#ffee02'
          },
          fontFamily: {
            sans: ['Poppins', 'sans-serif'],
            body: ['Roboto', 'sans-serif']
          }
        }
      }
    }
  </script>
</head>
<body>

<!-- Header Component -->
<header class="bg-white dark:bg-slate-800 shadow-md sticky top-0 z-50 transition-colors duration-300">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <a href="index.html" class="flex items-center">
        <h1 class="text-xl sm:text-2xl font-bold text-pink-600 dark:text-pink-400">Sisters for Good</h1>
      </a>

      <!-- Desktop Navigation (hidden on mobile) -->
      <nav class="hidden md:flex space-x-6">
        <a href="index.html" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Home</a>
        <a href="about.html" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">About Us</a>
        <a href="programs.html" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">Programs</a>
        <a href="events.html" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">Events</a>
        <a href="health.html" class="nav-link text-pink-600 dark:text-pink-400 font-semibold">Health Resources</a>
        <a href="donate.html" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">Donate</a>
        <a href="contact.html" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">Contact</a>
      </nav>

      <!-- Theme Toggle Button -->
      <button id="theme-toggle" class="theme-toggle hidden md:flex" aria-label="Toggle dark mode" title="Toggle dark/light theme">
        <svg class="sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
        </svg>
        <svg class="moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
        </svg>
      </button>

      <!-- Mobile menu button -->
      <button id="mobileMenuBtn" class="md:hidden flex items-center p-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-pink-600 dark:text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation (hidden by default) -->
    <div id="mobileMenu" class="md:hidden hidden mt-4 pb-4 border-t border-gray-200 dark:border-gray-600 pt-3">
      <nav class="flex flex-col space-y-4">
        <a href="index.html" class="nav-link text-center py-2 text-gray-700 dark:text-gray-300 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md">Home</a>
        <a href="about.html" class="nav-link text-center py-2 text-gray-700 dark:text-gray-300 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md">About Us</a>
        <a href="programs.html" class="nav-link text-center py-2 text-gray-700 dark:text-gray-300 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md">Programs</a>
        <a href="events.html" class="nav-link text-center py-2 text-gray-700 dark:text-gray-300 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md">Events</a>
        <a href="health.html" class="nav-link text-center py-2 bg-pink-50 dark:bg-gray-700 text-pink-600 dark:text-pink-400 font-medium rounded-md">Health Resources</a>
        <a href="donate.html" class="nav-link text-center py-2 text-gray-700 dark:text-gray-300 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md">Donate</a>
        <a href="contact.html" class="nav-link text-center py-2 text-gray-700 dark:text-gray-300 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md">Contact</a>
      </nav>
    </div>
  </div>
</header>

  <!-- Hero Section -->
  <section class="bg-gradient-to-r from-green-400 to-blue-500 text-white py-16 px-6">
    <div class="max-w-6xl mx-auto text-center">
      <h1 class="text-4xl md:text-5xl font-bold mb-6">🩺 Community Health Resources</h1>
      <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
        Your health matters. Access local healthcare services, support hotlines, and educational resources
        to keep our community strong and healthy.
      </p>
      <div class="flex justify-center gap-4 flex-wrap">
        <a href="#partners" class="bg-white text-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
          Find Local Partners
        </a>
        <a href="#hotlines" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors">
          Emergency Contacts
        </a>
      </div>
    </div>
  </section>

  <!-- Local Health Partners Section -->
  <section id="partners" class="py-16 px-6 bg-white dark:bg-slate-900 transition-colors duration-300">
    <div class="max-w-6xl mx-auto">
      <h2 class="text-3xl font-bold text-center mb-12 text-gray-800 dark:text-gray-100">🏥 Local Health Partners</h2>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Community Health & Dental -->
        <div class="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
              <span class="text-2xl">🦷</span>
            </div>
            <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">Community Health & Dental</h3>
          </div>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Comprehensive healthcare and dental services for the whole family.</p>
          <div class="space-y-2 text-sm">
            <p><strong>Services:</strong> Primary care, dental, pediatrics</p>
            <p><strong>Phone:</strong> <a href="tel:************" class="text-blue-600 hover:underline">(*************</a></p>
            <p><strong>Address:</strong> 247 East High Street, Pottstown, PA</p>
          </div>
          <button onclick="openDirections('247 East High Street, Pottstown, PA 19464')" class="mt-4 w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Get Directions
          </button>
        </div>

        <!-- WIC Program -->
        <div class="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4">
              <span class="text-2xl">🍎</span>
            </div>
            <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">WIC Program</h3>
          </div>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Nutrition assistance for women, infants, and children.</p>
          <div class="space-y-2 text-sm">
            <p><strong>Services:</strong> Nutrition education, food assistance</p>
            <p><strong>Phone:</strong> <a href="tel:************" class="text-blue-600 hover:underline">(*************</a></p>
            <p><strong>Eligibility:</strong> Pregnant/nursing mothers, children under 5</p>
          </div>
          <button onclick="openWICEligibility()" class="mt-4 w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors">
            Check Eligibility
          </button>
        </div>

        <!-- Birthright -->
        <div class="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-pink-100 dark:bg-pink-900 rounded-lg flex items-center justify-center mr-4">
              <span class="text-2xl">👶</span>
            </div>
            <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">Birthright</h3>
          </div>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Pregnancy support and resources for expecting mothers.</p>
          <div class="space-y-2 text-sm">
            <p><strong>Services:</strong> Pregnancy tests, counseling, support</p>
            <p><strong>Phone:</strong> <a href="tel:************" class="text-blue-600 hover:underline">(*************</a></p>
            <p><strong>Hours:</strong> Mon-Fri 9AM-5PM</p>
          </div>
          <button onclick="openBirthrightInfo()" class="mt-4 w-full bg-pink-600 text-white py-2 rounded-lg hover:bg-pink-700 transition-colors">
            Learn More
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Information & Hotlines -->
  <section id="hotlines" class="py-16 px-6 bg-gray-100 dark:bg-slate-800 transition-colors duration-300">
    <div class="max-w-6xl mx-auto">
      <h2 class="text-3xl font-bold text-center mb-12 text-gray-800 dark:text-gray-100">📞 Emergency Contacts & Support Hotlines</h2>

      <div class="grid md:grid-cols-2 gap-8">
        <!-- Emergency Services -->
        <div class="bg-red-50 border-l-4 border-red-500 p-6 rounded-lg">
          <h3 class="text-xl font-bold text-red-800 mb-4">🚨 Emergency Services</h3>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="font-semibold">Emergency (Police/Fire/EMS)</span>
              <a href="tel:911" class="text-red-600 font-bold text-lg hover:underline">911</a>
            </div>
            <div class="flex justify-between items-center">
              <span class="font-semibold">Poison Control</span>
              <a href="tel:1-************" class="text-red-600 font-bold hover:underline">1-************</a>
            </div>
            <div class="flex justify-between items-center">
              <span class="font-semibold">Crisis Text Line</span>
              <span class="text-red-600 font-bold">Text HOME to 741741</span>
            </div>
          </div>
        </div>

        <!-- Mental Health Support -->
        <div class="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-lg">
          <h3 class="text-xl font-bold text-blue-800 mb-4">🧠 Mental Health Support</h3>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="font-semibold">National Suicide Prevention</span>
              <a href="tel:988" class="text-blue-600 font-bold text-lg hover:underline">988</a>
            </div>
            <div class="flex justify-between items-center">
              <span class="font-semibold">NAMI Helpline</span>
              <a href="tel:**************" class="text-blue-600 font-bold hover:underline">**************</a>
            </div>
            <div class="flex justify-between items-center">
              <span class="font-semibold">Postpartum Support</span>
              <a href="tel:**************" class="text-blue-600 font-bold hover:underline">**************</a>
            </div>
          </div>
        </div>

        <!-- Tobacco Cessation -->
        <div class="bg-green-50 border-l-4 border-green-500 p-6 rounded-lg">
          <h3 class="text-xl font-bold text-green-800 mb-4">🚭 Tobacco Cessation</h3>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="font-semibold">PA Free Quitline</span>
              <a href="tel:**************" class="text-green-600 font-bold hover:underline">1-800-QUIT-NOW</a>
            </div>
            <div class="flex justify-between items-center">
              <span class="font-semibold">Text Support</span>
              <span class="text-green-600 font-bold">Text QUIT to 47848</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="font-semibold">Online Support</span>
              <a href="https://smokefree.gov" class="text-green-600 font-bold hover:underline">smokefree.gov</a>
            </div>
          </div>
        </div>

        <!-- Women's Health -->
        <div class="bg-purple-50 border-l-4 border-purple-500 p-6 rounded-lg">
          <h3 class="text-xl font-bold text-purple-800 mb-4">👩‍⚕️ Women's Health</h3>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="font-semibold">Planned Parenthood</span>
              <a href="tel:**************" class="text-purple-600 font-bold hover:underline">1-800-230-PLAN</a>
            </div>
            <div class="flex justify-between items-center">
              <span class="font-semibold">Domestic Violence Hotline</span>
              <a href="tel:**************" class="text-purple-600 font-bold hover:underline">1-800-799-SAFE</a>
            </div>
            <div class="flex justify-between items-center">
              <span class="font-semibold">Breastfeeding Support</span>
              <a href="tel:**************" class="text-purple-600 font-bold hover:underline">**************</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Downloadable Resources -->
  <section id="resources" class="py-16 px-6 bg-white dark:bg-slate-900 transition-colors duration-300">
    <div class="max-w-6xl mx-auto">
      <h2 class="text-3xl font-bold text-center mb-12 text-gray-800 dark:text-gray-100">📋 Downloadable Resources</h2>

      <div class="grid md:grid-cols-3 gap-8">
        <!-- Health Education -->
        <div class="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-6 transition-colors duration-300">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-3xl">📚</span>
            </div>
            <h3 class="text-xl font-bold text-gray-800">Health Education</h3>
          </div>
          <div class="space-y-3">
            <button onclick="downloadResource('nutrition-guide')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">Nutrition Guide for Families</span>
                <span class="text-blue-600">📄 PDF</span>
              </div>
            </button>
            <button onclick="downloadResource('pregnancy-exercise')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">Exercise During Pregnancy</span>
                <span class="text-blue-600">📄 PDF</span>
              </div>
            </button>
            <button onclick="downloadResource('mental-health')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">Mental Health Resources</span>
                <span class="text-blue-600">📄 PDF</span>
              </div>
            </button>
          </div>
        </div>

        <!-- Tobacco Cessation -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-3xl">🚭</span>
            </div>
            <h3 class="text-xl font-bold text-gray-800">Tobacco Cessation</h3>
          </div>
          <div class="space-y-3">
            <button onclick="downloadResource('quit-smoking')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">Quit Smoking Plan</span>
                <span class="text-green-600">📄 PDF</span>
              </div>
            </button>
            <button onclick="downloadResource('secondhand-smoke')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">Secondhand Smoke Facts</span>
                <span class="text-green-600">📄 PDF</span>
              </div>
            </button>
            <button onclick="downloadResource('nicotine-replacement')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">Nicotine Replacement Guide</span>
                <span class="text-green-600">📄 PDF</span>
              </div>
            </button>
          </div>
        </div>

        <!-- Community Flyers -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-3xl">📢</span>
            </div>
            <h3 class="text-xl font-bold text-gray-800">Community Flyers</h3>
          </div>
          <div class="space-y-3">
            <button onclick="downloadResource('fathers-day-flyer')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">Father's Day Brunch Flyer</span>
                <span class="text-purple-600">📄 PDF</span>
              </div>
            </button>
            <button onclick="downloadResource('tobacco-day-flyer')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">World No Tobacco Day</span>
                <span class="text-purple-600">📄 PDF</span>
              </div>
            </button>
            <button onclick="downloadResource('health-fair')" class="block w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
              <div class="flex items-center justify-between">
                <span class="font-medium">Health Fair Information</span>
                <span class="text-purple-600">📄 PDF</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Location Information -->
  <section id="locations" class="py-16 px-6 bg-gray-100">
    <div class="max-w-6xl mx-auto">
      <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">📍 Health Facility Locations</h2>

      <div class="grid lg:grid-cols-2 gap-8">
        <!-- Map Section -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <h3 class="text-xl font-bold mb-4 text-gray-800">Interactive Map</h3>
          <div class="bg-gray-200 rounded-lg h-64 flex items-center justify-center mb-4">
            <div class="text-center text-gray-600">
              <span class="text-4xl mb-2 block">🗺️</span>
              <p class="font-medium">Google Maps Integration</p>
              <p class="text-sm">Click locations below to view on map</p>
            </div>
          </div>
          <button onclick="viewFullMap()" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
            View Full Map
          </button>
        </div>

        <!-- Quick Directions -->
        <div class="space-y-6">
          <!-- Community Health & Dental -->
          <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-start justify-between mb-4">
              <div>
                <h4 class="font-bold text-gray-800">Community Health & Dental</h4>
                <p class="text-gray-600 text-sm">Primary Care & Dental Services</p>
              </div>
              <span class="text-2xl">🏥</span>
            </div>
            <div class="space-y-2 text-sm">
              <p><strong>Address:</strong> 247 East High Street, Pottstown, PA 19464</p>
              <p><strong>Parking:</strong> Free parking available on-site</p>
              <p><strong>Public Transit:</strong> PART Bus Route 1 & 3</p>
            </div>
            <div class="flex gap-2 mt-4">
              <button onclick="openDirections('247 East High Street, Pottstown, PA 19464')" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                Get Directions
              </button>
              <button onclick="callPhone('************')" class="flex-1 border border-blue-600 text-blue-600 py-2 px-4 rounded-lg text-sm hover:bg-blue-50 transition-colors">
                Call Now
              </button>
            </div>
          </div>

          <!-- WIC Office -->
          <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-start justify-between mb-4">
              <div>
                <h4 class="font-bold text-gray-800">WIC Program Office</h4>
                <p class="text-gray-600 text-sm">Nutrition Assistance Program</p>
              </div>
              <span class="text-2xl">🍎</span>
            </div>
            <div class="space-y-2 text-sm">
              <p><strong>Address:</strong> 247 East High Street, Pottstown, PA 19464</p>
              <p><strong>Hours:</strong> Mon-Fri 8:00 AM - 4:30 PM</p>
              <p><strong>Appointments:</strong> Required for all visits</p>
            </div>
            <div class="flex gap-2 mt-4">
              <button onclick="openDirections('247 East High Street, Pottstown, PA 19464')" class="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-green-700 transition-colors">
                Get Directions
              </button>
              <button onclick="scheduleAppointment('wic')" class="flex-1 border border-green-600 text-green-600 py-2 px-4 rounded-lg text-sm hover:bg-green-50 transition-colors">
                Schedule Appointment
              </button>
            </div>
          </div>

          <!-- Birthright -->
          <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-start justify-between mb-4">
              <div>
                <h4 class="font-bold text-gray-800">Birthright Pottstown</h4>
                <p class="text-gray-600 text-sm">Pregnancy Support Services</p>
              </div>
              <span class="text-2xl">👶</span>
            </div>
            <div class="space-y-2 text-sm">
              <p><strong>Address:</strong> 518 High Street, Pottstown, PA 19464</p>
              <p><strong>Hours:</strong> Mon-Fri 9:00 AM - 5:00 PM</p>
              <p><strong>Services:</strong> Free pregnancy tests, counseling</p>
            </div>
            <div class="flex gap-2 mt-4">
              <button onclick="openDirections('518 High Street, Pottstown, PA 19464')" class="flex-1 bg-pink-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-pink-700 transition-colors">
                Get Directions
              </button>
              <button onclick="scheduleAppointment('birthright')" class="flex-1 border border-pink-600 text-pink-600 py-2 px-4 rounded-lg text-sm hover:bg-pink-50 transition-colors">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-800 dark:bg-gray-900 text-white dark:text-gray-300 py-12 px-6 transition-colors duration-300">
    <div class="max-w-6xl mx-auto">
      <div class="grid md:grid-cols-3 gap-8">
        <div>
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
              <span class="text-white font-bold">S</span>
            </div>
            <h3 class="text-xl font-bold text-white dark:text-gray-100">Sisters for Good</h3>
          </div>
          <p class="text-gray-300 dark:text-gray-400 mb-4">
            Empowering our community through health education, support, and accessible resources.
          </p>
        </div>

        <div>
          <h4 class="font-bold mb-4 text-white dark:text-gray-100">Quick Links</h4>
          <div class="space-y-2">
            <a href="index.html" class="block text-gray-300 dark:text-gray-400 hover:text-white dark:hover:text-gray-200 transition-colors">Home</a>
            <a href="events.html" class="block text-gray-300 dark:text-gray-400 hover:text-white dark:hover:text-gray-200 transition-colors">Events</a>
            <a href="health.html" class="block text-gray-300 dark:text-gray-400 hover:text-white dark:hover:text-gray-200 transition-colors">Health Resources</a>
            <a href="contact.html" class="block text-gray-300 dark:text-gray-400 hover:text-white dark:hover:text-gray-200 transition-colors">Contact</a>
          </div>
        </div>

        <div>
          <h4 class="font-bold mb-4 text-white dark:text-gray-100">Emergency Contacts</h4>
          <div class="space-y-2 text-sm">
            <p><strong>Emergency:</strong> <a href="tel:911" class="text-red-400 hover:text-red-300">911</a></p>
            <p><strong>Crisis Line:</strong> <a href="tel:988" class="text-blue-400 hover:text-blue-300">988</a></p>
            <p><strong>Poison Control:</strong> <a href="tel:1-************" class="text-green-400 hover:text-green-300">1-************</a></p>
          </div>
        </div>
      </div>

      <div class="border-t border-gray-700 dark:border-gray-600 mt-8 pt-8 text-center text-gray-400 dark:text-gray-500">
        <p>&copy; 2025 Sisters for Good. All rights reserved. | Serving the Pottstown community with love and care.</p>
      </div>
    </div>
  </footer>

  <!-- JavaScript Functions -->
  <script>
    // Mobile menu functionality (matching index.html)
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuBtn = document.getElementById('mobileMenuBtn');
      const mobileMenu = document.getElementById('mobileMenu');

      if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
          mobileMenu.classList.toggle('hidden');
        });
      }

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });
    });

    // Open directions in Google Maps
    function openDirections(address) {
      const encodedAddress = encodeURIComponent(address);
      const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
      window.open(mapsUrl, '_blank');
    }

    // Open WIC eligibility information
    function openWICEligibility() {
      const message = `WIC Eligibility Requirements:\n\n` +
        `• Pregnant women\n` +
        `• Breastfeeding women (up to 1 year postpartum)\n` +
        `• Non-breastfeeding postpartum women (up to 6 months)\n` +
        `• Infants and children up to age 5\n` +
        `• Must meet income guidelines\n` +
        `• Must be at nutritional risk\n\n` +
        `Call (************* to schedule an appointment and learn more about income guidelines.`;

      alert(message);
    }

    // Open Birthright information
    function openBirthrightInfo() {
      const message = `Birthright Services:\n\n` +
        `• Free pregnancy tests\n` +
        `• Confidential counseling\n` +
        `• Emotional support\n` +
        `• Resource referrals\n` +
        `• Material assistance\n\n` +
        `All services are free and confidential.\n` +
        `Call (************* for more information.`;

      alert(message);
    }

    // Download resource function
    function downloadResource(resourceType) {
      // In a real implementation, these would link to actual PDF files
      const resources = {
        'nutrition-guide': 'This would download the Nutrition Guide for Families PDF',
        'pregnancy-exercise': 'This would download the Exercise During Pregnancy PDF',
        'mental-health': 'This would download the Mental Health Resources PDF',
        'quit-smoking': 'This would download the Quit Smoking Plan PDF',
        'secondhand-smoke': 'This would download the Secondhand Smoke Facts PDF',
        'nicotine-replacement': 'This would download the Nicotine Replacement Guide PDF',
        'fathers-day-flyer': 'This would download the Father\'s Day Brunch Flyer PDF',
        'tobacco-day-flyer': 'This would download the World No Tobacco Day Flyer PDF',
        'health-fair': 'This would download the Health Fair Information PDF'
      };

      const message = resources[resourceType] || 'Resource not found';
      alert(`${message}\n\nNote: In the full implementation, this would automatically download the PDF file.`);
    }

    // Make location buttons functional
    function callPhone(number) {
      window.location.href = `tel:${number}`;
    }

    function scheduleAppointment(service) {
      let message = '';
      switch(service) {
        case 'wic':
          message = 'To schedule a WIC appointment:\n\nCall: (*************\nHours: Mon-Fri 8:00 AM - 4:30 PM\n\nPlease have your ID and proof of income ready.';
          break;
        case 'birthright':
          message = 'To learn more about Birthright services:\n\nCall: (*************\nHours: Mon-Fri 9:00 AM - 5:00 PM\n\nWalk-ins welcome during business hours.';
          break;
        default:
          message = 'Please call the facility directly to schedule an appointment.';
      }
      alert(message);
    }

    // Interactive map functionality
    function viewFullMap() {
      const locations = [
        'Community Health & Dental, 247 East High Street, Pottstown, PA 19464',
        'WIC Program Office, 247 East High Street, Pottstown, PA 19464',
        'Birthright Pottstown, 518 High Street, Pottstown, PA 19464'
      ];

      const searchQuery = locations.join(' | ');
      const encodedQuery = encodeURIComponent('Pottstown PA health services');
      const mapsUrl = `https://www.google.com/maps/search/${encodedQuery}`;
      window.open(mapsUrl, '_blank');
    }

    // Add click handlers for location buttons
    document.addEventListener('DOMContentLoaded', function() {
      // Get Directions buttons in location section
      const directionButtons = document.querySelectorAll('#locations button:contains("Get Directions")');

      // Call Now buttons
      const callButtons = document.querySelectorAll('#locations button:contains("Call Now")');

      // Schedule Appointment buttons
      const appointmentButtons = document.querySelectorAll('#locations button:contains("Schedule Appointment")');

      // View Full Map button
      const mapButton = document.querySelector('button:contains("View Full Map")');
      if (mapButton) {
        mapButton.onclick = viewFullMap;
      }
    });

    // Initialize AOS animations
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });
  </script>

  <!-- AOS Animation Library -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

</body>
</html>
