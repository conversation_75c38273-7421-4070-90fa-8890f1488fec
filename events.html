<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Stay updated on upcoming events and activities organized by Sisters for Good. Join us for workshops, seminars, fundraisers, and community gatherings.">
  <title>Sisters for Good | Events</title>

  <!-- Favicon -->
  <link rel="icon" href="favicon.ico" type="image/x-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#fc5c7d',
            secondary: '#6a82fb',
            accent: '#ffee02'
          },
          fontFamily: {
            sans: ['Poppins', 'sans-serif'],
            body: ['Roboto', 'sans-serif']
          }
        }
      }
    }
  </script>
</head>
<body>

<!-- Header Component -->
<header class="bg-white shadow-md sticky top-0 z-50">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <a href="index.html" class="flex items-center">
        <h1 class="text-xl sm:text-2xl font-bold text-pink-600">Sisters for Good</h1>
      </a>

      <!-- Desktop Navigation (hidden on mobile) -->
      <nav class="hidden md:flex space-x-6">
        <a href="index.html" class="nav-link hover:text-pink-600 transition-colors font-medium">Home</a>
        <a href="about.html" class="nav-link hover:text-pink-600 transition-colors">About Us</a>
        <a href="programs.html" class="nav-link hover:text-pink-600 transition-colors">Programs</a>
        <a href="events.html" class="nav-link text-pink-600 font-semibold">Events</a>
        <a href="health.html" class="nav-link hover:text-pink-600 transition-colors">Health Resources</a>
        <a href="donate.html" class="nav-link hover:text-pink-600 transition-colors">Donate</a>
        <a href="contact.html" class="nav-link hover:text-pink-600 transition-colors">Contact</a>
      </nav>

      <!-- Mobile menu button -->
      <button id="mobileMenuBtn" class="md:hidden flex items-center p-3 rounded-md hover:bg-gray-100 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation (hidden by default) -->
    <div id="mobileMenu" class="md:hidden hidden mt-4 pb-4 border-t border-gray-200 pt-3">
      <nav class="flex flex-col space-y-4">
        <a href="index.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Home</a>
        <a href="about.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">About Us</a>
        <a href="programs.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Programs</a>
        <a href="events.html" class="nav-link text-center py-2 bg-pink-50 text-pink-600 font-medium rounded-md">Events</a>
        <a href="health.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Health Resources</a>
        <a href="donate.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Donate</a>
        <a href="contact.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Contact</a>
      </nav>
    </div>
  </div>
</header>

<!-- Page Header -->
<section class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white py-16 relative overflow-hidden">
  <div class="absolute inset-0 bg-black opacity-10"></div>
  <div class="container mx-auto px-4 text-center relative z-10">
    <h1 class="text-4xl md:text-5xl font-bold mb-4 drop-shadow-lg" data-aos="fade-up">Events & Calendar</h1>
    <p class="text-xl max-w-3xl mx-auto leading-relaxed drop-shadow-md" data-aos="fade-up" data-aos-delay="100">Join us for workshops, seminars, fundraisers, and community gatherings throughout the year.</p>
    <div class="mt-8 flex flex-wrap justify-center items-center gap-4 md:gap-6" data-aos="fade-up" data-aos-delay="200">
      <div class="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 transform hover:scale-105 transition-transform">
        <span class="text-2xl">🎉</span>
        <span class="text-sm font-medium">Celebrations</span>
      </div>
      <div class="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 transform hover:scale-105 transition-transform">
        <span class="text-2xl">📚</span>
        <span class="text-sm font-medium">Workshops</span>
      </div>
      <div class="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 transform hover:scale-105 transition-transform">
        <span class="text-2xl">🤝</span>
        <span class="text-sm font-medium">Community</span>
      </div>
    </div>
  </div>
</section>

<!-- Events Overview -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="max-w-3xl mx-auto text-center mb-12">
      <p data-aos="fade-up">At Sisters for Good, we host a variety of events designed to bring our community together, share knowledge, and create meaningful connections. Check out our upcoming events and mark your calendar!</p>
    </div>
  </div>
</section>

<!-- World No Tobacco Day Section -->
<section id="tobacco-day" class="section bg-gradient-to-r from-green-400 to-blue-500 text-white">
  <div class="container mx-auto px-4">
    <div class="section-title text-white">
      <h2 data-aos="fade-up">🌬️ World No Tobacco Day 2025</h2>
      <p class="text-xl max-w-3xl mx-auto mt-4" data-aos="fade-up" data-aos-delay="100">
        Sisters for Good supports tobacco-free living and healthy communities through education and awareness
      </p>
    </div>

    <div class="grid md:grid-cols-2 gap-12 items-center">
      <!-- Content Section -->
      <div data-aos="fade-right">
        <div class="bg-white/20 backdrop-blur-sm rounded-xl p-8 border border-white/30">
          <h3 class="text-2xl font-bold mb-6 text-yellow-300">🚭 Breathe Free, Live Strong</h3>
          <p class="text-lg mb-6 leading-relaxed">
            Our sisters deserve clean air and healthy futures. Sisters for Good promotes tobacco-free living
            by sharing educational resources and supporting community members in their journey toward healthier lifestyles.
          </p>

          <div class="space-y-4 mb-8">
            <div class="flex items-start space-x-3">
              <span class="text-yellow-300 text-xl">📅</span>
              <div>
                <h4 class="font-bold">Awareness Day</h4>
                <p>Saturday, May 31st, 2025</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <span class="text-yellow-300 text-xl">🌍</span>
              <div>
                <h4 class="font-bold">Global Initiative</h4>
                <p>World Health Organization Campaign</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <span class="text-yellow-300 text-xl">📚</span>
              <div>
                <h4 class="font-bold">Our Role</h4>
                <p>Education and Resource Sharing</p>
              </div>
            </div>
          </div>

          <div class="flex flex-wrap gap-4">
            <a href="health.html" class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
              Health Resources
            </a>
            <a href="https://www.who.int/campaigns/world-no-tobacco-day" target="_blank" class="bg-gradient-to-r from-blue-500 to-teal-500 hover:from-blue-600 hover:to-teal-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
              Learn More (WHO)
            </a>
          </div>
        </div>
      </div>

      <!-- Image and Activities Section -->
      <div data-aos="fade-left">
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?fm=jpg&q=80&w=1200&ixlib=rb-4.0.3"
               alt="Confident Black woman embracing healthy lifestyle and wellness"
               class="w-full h-64 object-cover rounded-lg mb-6">

          <h4 class="text-xl font-bold mb-4 text-yellow-300">📚 Educational Resources</h4>
          <ul class="space-y-3">
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Tobacco cessation information and support</span>
            </li>
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Health effects of tobacco use</span>
            </li>
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Lung health and wellness education</span>
            </li>
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Local support group referrals</span>
            </li>
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Downloadable educational materials</span>
            </li>
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Links to professional cessation programs</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Featured Event -->
<section id="thanksgiving" class="section bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Featured Event</h2>
    </div>
    <div class="card" data-aos="fade-up">
      <div class="md:flex">
        <div class="md:w-1/3">
          <img src="https://images.unsplash.com/photo-1574672280600-4accfa5b6f98?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dGhhbmtzZ2l2aW5nJTIwZGlubmVyfGVufDB8fDB8fHww" alt="Thanksgiving Luncheon" class="w-full h-full object-cover rounded-l-lg">
        </div>
        <div class="md:w-2/3 p-6">
          <div class="bg-accent text-dark-color inline-block px-3 py-1 rounded-full text-sm font-bold mb-4">Upcoming</div>
          <h3 class="text-2xl font-bold mb-2">Thanksgiving Luncheon</h3>
          <p class="mb-4">Join us for a community Thanksgiving Luncheon hosted by Sisters for Good Foundation. This event brings together community members for a delicious meal and meaningful connections.</p>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <h4 class="font-bold">Date & Time</h4>
              <p>November 25, 2024</p>
              <p>1:00 PM - 3:00 PM</p>
            </div>
            <div>
              <h4 class="font-bold">Location</h4>
              <p>247E High Street</p>
              <p>Pottstown, PA 19464</p>
            </div>
          </div>
          <p class="mb-4">We will also have an informational seminar presented by Mrs. Barbara Lee Stanislaus on community resources and support services available during the holiday season.</p>
          <a href="mailto:<EMAIL>?subject=RSVP for Thanksgiving Luncheon" class="btn btn-primary">RSVP Now</a>

          <!-- Social Sharing Placeholder -->
          <div class="social-share-placeholder"></div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Father's Day Appreciation Brunch -->
<section id="fathers-day-rsvp" class="section bg-gradient-to-r from-amber-400 to-orange-500 text-white">
  <div class="container mx-auto px-4">
    <div class="section-title text-white">
      <h2 data-aos="fade-up">🍳 Father's Day Appreciation Brunch</h2>
      <p class="text-xl max-w-3xl mx-auto mt-4" data-aos="fade-up" data-aos-delay="100">
        Celebrating the kings who show up every day! Join us for a special brunch honoring fathers, father figures, and male mentors.
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-12 items-start">
      <!-- Event Details -->
      <div data-aos="fade-right">
        <div class="bg-white/20 backdrop-blur-sm rounded-xl p-8 border border-white/30">
          <h3 class="text-2xl font-bold mb-6 text-yellow-200">📅 Event Details</h3>

          <div class="space-y-4 mb-8">
            <div class="flex items-start space-x-3">
              <span class="text-yellow-200 text-xl">📅</span>
              <div>
                <h4 class="font-bold">Date</h4>
                <p>Sunday, June 15th, 2025</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <span class="text-yellow-200 text-xl">🕐</span>
              <div>
                <h4 class="font-bold">Time</h4>
                <p>10:00 AM - 11:00 AM</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <span class="text-yellow-200 text-xl">📍</span>
              <div>
                <h4 class="font-bold">Location</h4>
                <p>247 East High Street, Pottstown, PA 19464</p>
              </div>
            </div>
          </div>

          <h4 class="text-xl font-bold mb-4 text-yellow-200">🎉 What to Expect</h4>
          <ul class="space-y-3 mb-8">
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Complimentary Brunch</span>
            </li>
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Father Appreciation Ceremony</span>
            </li>
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Community Fellowship</span>
            </li>
            <li class="flex items-center space-x-3">
              <span class="text-green-300">✓</span>
              <span>Special Gifts for Dads</span>
            </li>
          </ul>

          <button onclick="downloadFathersFlyer()" class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 w-full">
            📄 Download Event Flyer
          </button>
        </div>
      </div>

      <!-- RSVP Form -->
      <div data-aos="fade-left">
        <div class="bg-white rounded-xl p-8 shadow-2xl">
          <h3 class="text-2xl font-bold mb-6 text-gray-800">RSVP for Father's Day Brunch</h3>

          <form id="fathers-day-form" class="space-y-6">
            <div class="grid md:grid-cols-2 gap-4">
              <div>
                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                <input type="text" id="firstName" name="firstName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent">
              </div>
              <div>
                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                <input type="text" id="lastName" name="lastName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent">
              </div>
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
              <input type="email" id="email" name="email" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent">
            </div>

            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
              <input type="tel" id="phone" name="phone"
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent">
            </div>

            <div>
              <label for="attendees" class="block text-sm font-medium text-gray-700 mb-2">Number of Attendees *</label>
              <select id="attendees" name="attendees" required
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent">
                <option value="">Select number of attendees</option>
                <option value="1">1 person</option>
                <option value="2">2 people</option>
                <option value="3">3 people</option>
                <option value="4">4 people</option>
                <option value="5">5 people</option>
                <option value="6+">6 or more people</option>
              </select>
            </div>

            <div>
              <label for="dietary" class="block text-sm font-medium text-gray-700 mb-2">Dietary Restrictions/Preferences</label>
              <textarea id="dietary" name="dietary" rows="3"
                        placeholder="Please let us know about any dietary restrictions, allergies, or special meal preferences..."
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"></textarea>
            </div>

            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Special Message (Optional)</label>
              <textarea id="message" name="message" rows="3"
                        placeholder="Share a special message about the father figure you're celebrating..."
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"></textarea>
            </div>

            <button type="submit"
                    class="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
              🎉 RSVP Now - Reserve Your Spot!
            </button>
          </form>

          <div id="rsvp-success" class="hidden mt-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <h4 class="font-bold">✅ RSVP Confirmed!</h4>
            <p>Thank you for registering! We'll send you a confirmation email with event details.</p>
            <p class="mt-2"><strong>Questions?</strong> Contact us at <a href="tel:************" class="text-green-600 hover:underline">(*************</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Upcoming Events -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Upcoming Events</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Event 1 -->
      <div class="card" data-aos="fade-up">
        <div class="p-6">
          <div class="bg-accent text-dark-color inline-block px-3 py-1 rounded-full text-sm font-bold mb-4">Workshop</div>
          <h3 class="text-xl font-bold mb-2">Financial Literacy Workshop</h3>
          <p class="mb-4">Learn essential financial skills including budgeting, saving, and investing for your future.</p>
          <div class="mb-4">
            <p><strong>Date:</strong> December 10, 2024</p>
            <p><strong>Time:</strong> 6:00 PM - 8:00 PM</p>
            <p><strong>Location:</strong> Sisters for Good Main Office</p>
          </div>
          <button class="btn btn-primary event-register-btn" data-event-id="event1" data-event-name="Financial Literacy Workshop">Register Now</button>
        </div>
      </div>

      <!-- Event 2 -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="p-6">
          <div class="bg-accent text-dark-color inline-block px-3 py-1 rounded-full text-sm font-bold mb-4">Seminar</div>
          <h3 class="text-xl font-bold mb-2">Women's Health Seminar</h3>
          <p class="mb-4">Join healthcare professionals for a discussion on women's health issues and preventative care.</p>
          <div class="mb-4">
            <p><strong>Date:</strong> January 15, 2025</p>
            <p><strong>Time:</strong> 5:30 PM - 7:30 PM</p>
            <p><strong>Location:</strong> Pottstown Community Center</p>
          </div>
          <button class="btn btn-primary event-register-btn" data-event-id="event2" data-event-name="Women's Health Seminar">Register Now</button>
        </div>
      </div>

      <!-- Event 3 -->
      <div class="card" data-aos="fade-up" data-aos-delay="200">
        <div class="p-6">
          <div class="bg-accent text-dark-color inline-block px-3 py-1 rounded-full text-sm font-bold mb-4">Fundraiser</div>
          <h3 class="text-xl font-bold mb-2">Annual Charity Gala</h3>
          <p class="mb-4">Our biggest fundraising event of the year featuring dinner, entertainment, and a silent auction.</p>
          <div class="mb-4">
            <p><strong>Date:</strong> February 28, 2025</p>
            <p><strong>Time:</strong> 7:00 PM - 10:00 PM</p>
            <p><strong>Location:</strong> Grand Ballroom, Pottstown Hotel</p>
          </div>
          <button class="btn btn-primary event-register-btn" data-event-id="event3" data-event-name="Annual Charity Gala">Register Now</button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Past Events -->
<section class="section bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Past Events</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Past Event 1 -->
      <div class="card" data-aos="fade-up">
        <div class="p-6">
          <div class="bg-gray-500 text-white inline-block px-3 py-1 rounded-full text-sm font-bold mb-4">Workshop</div>
          <h3 class="text-xl font-bold mb-2">Resume Building Workshop</h3>
          <p class="mb-4">Participants learned how to create effective resumes and cover letters for job applications.</p>
          <div class="mb-4">
            <p><strong>Date:</strong> October 5, 2024</p>
            <p><strong>Location:</strong> Sisters for Good Main Office</p>
          </div>
          <div class="flex items-center">
            <span class="text-primary mr-2">Highlights:</span>
            <span>30 participants, 5 job offers secured</span>
          </div>
        </div>
      </div>

      <!-- Past Event 2 -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="p-6">
          <div class="bg-gray-500 text-white inline-block px-3 py-1 rounded-full text-sm font-bold mb-4">Community Event</div>
          <h3 class="text-xl font-bold mb-2">Back to School Supply Drive</h3>
          <p class="mb-4">Collected and distributed school supplies to children in need throughout the community.</p>
          <div class="mb-4">
            <p><strong>Date:</strong> August 15, 2024</p>
            <p><strong>Location:</strong> Pottstown Community Park</p>
          </div>
          <div class="flex items-center">
            <span class="text-primary mr-2">Highlights:</span>
            <span>200+ children served, 15 local businesses participated</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Calendar Section -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Event Calendar</h2>
    </div>
    <div class="card p-6" data-aos="fade-up">
      <p class="text-center mb-6">View our full calendar of events to stay updated on all Sisters for Good activities.</p>
      <div class="text-center">
        <a href="#" class="btn btn-primary">View Full Calendar</a>
      </div>
      <p class="text-center mt-6 text-sm">For the most up-to-date information on our events, please follow us on social media or contact us directly.</p>
    </div>
  </div>
</section>

<!-- Host an Event CTA -->
<section class="section bg-primary text-white text-center">
  <div class="container mx-auto px-4">
    <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Host an Event with Us</h2>
    <p class="max-w-2xl mx-auto mb-8" data-aos="fade-up" data-aos-delay="100">Interested in hosting a workshop, seminar, or fundraiser with Sisters for Good? We welcome collaboration opportunities with individuals, businesses, and organizations that share our mission.</p>
    <a href="contact.html" class="btn btn-accent text-dark-color" data-aos="fade-up" data-aos-delay="200">Contact Us</a>
  </div>
</section>

<!-- Footer Component -->
<footer class="bg-gray-800 text-white py-10">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Organization Info -->
      <div>
        <h3 class="text-xl font-bold mb-4">Sisters for Good</h3>
        <p class="mb-2">Empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach.</p>
        <p class="text-sm mt-4">&copy; 2025 Sisters for Good. All Rights Reserved.</p>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-xl font-bold mb-4">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="index.html" class="hover:text-pink-400 transition-colors">Home</a></li>
          <li><a href="about.html" class="hover:text-pink-400 transition-colors">About Us</a></li>
          <li><a href="programs.html" class="hover:text-pink-400 transition-colors">Programs</a></li>
          <li><a href="events.html" class="hover:text-pink-400 transition-colors">Events</a></li>
          <li><a href="health.html" class="hover:text-pink-400 transition-colors">Health Resources</a></li>
          <li><a href="donate.html" class="hover:text-pink-400 transition-colors">Donate</a></li>
          <li><a href="contact.html" class="hover:text-pink-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Information -->
      <div>
        <h3 class="text-xl font-bold mb-4">Contact Us</h3>
        <address class="not-italic">
          <p class="mb-2">📍 247 East High St, Pottstown, PA 19464</p>
          <p class="mb-2">📞 (*************</p>
          <p class="mb-2">📧 <a href="mailto:<EMAIL>" class="hover:text-pink-400 transition-colors"><EMAIL></a></p>
        </address>

        <!-- Social Media Links -->
        <div class="mt-4 flex space-x-4">
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Facebook</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Instagram</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772a4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Twitter</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>

<!-- Chatbot Widget -->
<div id="chatbot-container" class="fixed bottom-4 right-4 w-80 bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300 z-50 chatbot-minimized">
  <div class="bg-primary text-white p-3 flex justify-between items-center cursor-pointer" id="chatbot-toggle">
    <h3 class="font-bold">Sisters for Good Assistant</h3>
    <div class="flex items-center">
      <span class="text-xl mr-2 transition-transform duration-300">▲</span>
      <span class="chatbot-close" id="chatbot-close">×</span>
    </div>
  </div>
  <div class="p-4 h-96 flex flex-col">
    <div id="chatbot-messages" class="flex-1 overflow-y-auto mb-4"></div>
    <div class="flex">
      <input type="text" id="chatbot-input" class="flex-1 border rounded-l px-3 py-2" placeholder="Ask a question...">
      <button id="chatbot-send" class="bg-primary text-white px-4 py-2 rounded-r">Send</button>
    </div>
  </div>
</div>

<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>
<script src="js/chatbot.js"></script>

<!-- Initialize Scripts -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile menu
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');

    if (mobileMenuBtn && mobileMenu) {
      mobileMenuBtn.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Initialize AOS animations
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });

    // Father's Day RSVP form handling
    const fathersForm = document.getElementById('fathers-day-form');
    if (fathersForm) {
      fathersForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleFathersDayRSVP();
      });
    }
  });

  // Handle Father's Day RSVP submission
  function handleFathersDayRSVP() {
    const form = document.getElementById('fathers-day-form');
    const successDiv = document.getElementById('rsvp-success');

    // Get form data
    const formData = new FormData(form);
    const rsvpData = {
      firstName: formData.get('firstName'),
      lastName: formData.get('lastName'),
      email: formData.get('email'),
      phone: formData.get('phone'),
      attendees: formData.get('attendees'),
      dietary: formData.get('dietary'),
      message: formData.get('message'),
      event: 'Father\'s Day Appreciation Brunch',
      date: 'Sunday, June 15th, 2025',
      time: '10:00 AM - 11:00 AM'
    };

    // Simulate form submission (in real implementation, this would send to a server)
    console.log('RSVP Data:', rsvpData);

    // Show success message
    form.style.display = 'none';
    successDiv.classList.remove('hidden');

    // Send confirmation email (simulation)
    sendRSVPConfirmation(rsvpData);

    // Scroll to success message
    successDiv.scrollIntoView({ behavior: 'smooth' });
  }

  // Simulate sending RSVP confirmation
  function sendRSVPConfirmation(data) {
    // In a real implementation, this would integrate with an email service
    console.log('Sending confirmation email to:', data.email);

    // Create mailto link as fallback
    const subject = encodeURIComponent('Father\'s Day Brunch RSVP Confirmation');
    const body = encodeURIComponent(`
Dear ${data.firstName} ${data.lastName},

Thank you for your RSVP to the Father's Day Appreciation Brunch!

Event Details:
- Date: ${data.date}
- Time: ${data.time}
- Location: 247 East High Street, Pottstown, PA 19464
- Number of Attendees: ${data.attendees}

We look forward to celebrating with you!

Best regards,
Sisters for Good Team
(*************
<EMAIL>
    `);

    // Optional: Open email client for manual sending
    // window.location.href = `mailto:${data.email}?subject=${subject}&body=${body}`;
  }

  // Download Father's Day flyer
  function downloadFathersFlyer() {
    // In a real implementation, this would link to an actual PDF file
    const flyerContent = `
🍳 FATHER'S DAY APPRECIATION BRUNCH 🍳

Join Sisters for Good for a special celebration!

📅 Sunday, June 15th, 2025
🕐 10:00 AM - 11:00 AM
📍 247 East High Street, Pottstown, PA 19464

🎉 What's Included:
✓ Complimentary Brunch
✓ Father Appreciation Ceremony
✓ Community Fellowship
✓ Special Gifts for Dads

Celebrating the kings who show up every day!

RSVP: Visit our website or call (*************
Email: <EMAIL>

#SistersForGood #FathersDay #CommunityLove
    `;

    // Create a downloadable text file (in real implementation, this would be a PDF)
    const blob = new Blob([flyerContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'fathers-day-brunch-flyer.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    // Show download notification
    alert('Father\'s Day Brunch flyer downloaded!\n\nNote: In the full implementation, this would be a professionally designed PDF flyer.');
  }

  // Smooth scrolling for anchor links
  document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  });
</script>

<!-- Social Sharing Script -->
<script src="js/social-share.js"></script>

<!-- Event Registration Script -->
<script src="js/event-registration.js"></script>
</body>
</html>
