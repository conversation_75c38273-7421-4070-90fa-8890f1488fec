/**
 * Sisters for Good - Chatbot
 * A simple chatbot that answers questions about the organization
 * with added wisdom and insights
 */

class SFGChatbot {
  constructor() {
    this.chatContainer = document.getElementById('chatbot-container');
    this.chatMessages = document.getElementById('chatbot-messages');
    this.chatInput = document.getElementById('chatbot-input');
    this.chatButton = document.getElementById('chatbot-send');
    this.chatToggle = document.getElementById('chatbot-toggle');
    this.chatClose = document.getElementById('chatbot-close');
    this.faqData = null;
    this.wisdomQuotes = [
      "The future belongs to those who believe in the beauty of their dreams. - <PERSON>",
      "When women support each other, incredible things happen.",
      "Empowered women empower women.",
      "The question isn't who's going to let me; it's who's going to stop me. - A<PERSON> <PERSON>",
      "A strong woman stands up for herself. A stronger woman stands up for everyone else.",
      "The most effective way to do it, is to do it. - <PERSON>",
      "We rise by lifting others.",
      "The power of unity makes us stronger together.",
      "Your potential is endless. Go do what you were created to do.",
      "Behind every successful woman is a tribe of other successful women who have her back."
    ];

    this.initialize();
  }

  initialize() {
    // Load FAQ data
    this.loadFAQData();

    // Set up event listeners
    if (this.chatButton) {
      this.chatButton.addEventListener('click', () => this.handleUserInput());
    }

    if (this.chatInput) {
      this.chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.handleUserInput();
        }
      });
    }

    if (this.chatToggle) {
      this.chatToggle.addEventListener('click', () => this.toggleChatbot());
    }

    if (this.chatClose) {
      this.chatClose.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent the toggle event from firing
        this.closeChatbot();
      });
    }

    // Add welcome message
    this.addBotMessage("Hello! I'm here to help answer questions about Sisters for Good. How can I assist you today?");
  }

  loadFAQData() {
    fetch('data/faq.json')
      .then(response => response.json())
      .then(data => {
        this.faqData = data;
        console.log('FAQ data loaded successfully');
      })
      .catch(error => {
        console.error('Error loading FAQ data:', error);
        // Fallback to basic responses if JSON fails to load
        this.faqData = {
          "questions": [
            {
              "question": "What is Sisters for Good?",
              "answer": "Sisters for Good is a non-profit organization dedicated to empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach."
            },
            {
              "question": "How can I donate?",
              "answer": "You can donate through our website using cryptocurrency (Bitcoin, Ethereum, or Doge) or traditional methods. Visit our Donate page for more information."
            },
            {
              "question": "How can I get involved?",
              "answer": "You can get involved by volunteering, donating, or spreading awareness. Visit our Get Involved page to learn more about opportunities."
            }
          ]
        };
      });
  }

  handleUserInput() {
    if (!this.chatInput || !this.chatMessages) return;

    const userMessage = this.chatInput.value.trim();
    if (userMessage === '') return;

    // Add user message to chat
    this.addUserMessage(userMessage);

    // Clear input
    this.chatInput.value = '';

    // Process and respond
    setTimeout(() => {
      const response = this.generateResponse(userMessage);
      this.addBotMessage(response);

      // Occasionally add a wisdom quote after the response
      if (Math.random() < 0.3) { // 30% chance to add wisdom
        setTimeout(() => {
          this.addBotMessage(this.getRandomWisdom());
        }, 1000);
      }
    }, 500);
  }

  getRandomWisdom() {
    return this.wisdomQuotes[Math.floor(Math.random() * this.wisdomQuotes.length)];
  }

  generateResponse(userMessage) {
    if (!this.faqData) {
      return "I'm sorry, I'm having trouble accessing my knowledge base. Please try again later or contact us <NAME_EMAIL>.";
    }

    // Convert to lowercase for case-insensitive matching
    const message = userMessage.toLowerCase();

    // Check for wisdom or inspiration requests
    if (message.includes('wisdom') || message.includes('inspire') ||
        message.includes('quote') || message.includes('motivation')) {
      return "Here's some wisdom to inspire you: " + this.getRandomWisdom();
    }

    // Check for matches in FAQ data
    for (const item of this.faqData.questions) {
      // Simple keyword matching
      if (message.includes(item.question.toLowerCase())) {
        return item.answer;
      }
    }

    // Check for common keywords
    if (message.includes('donate') || message.includes('contribution') || message.includes('money')) {
      return "You can donate to Sisters for Good through cryptocurrency (Bitcoin, Ethereum, or Doge) or traditional methods. Visit our Donate page for more information. Remember, even small contributions can make a big difference in someone's life.";
    }

    if (message.includes('volunteer') || message.includes('help') || message.includes('participate')) {
      return "We welcome volunteers! Please visit our Get Involved page to learn about current opportunities or contact us <NAME_EMAIL>. Your time and talents are valuable gifts that can transform lives and strengthen our community.";
    }

    if (message.includes('contact') || message.includes('email') || message.includes('phone')) {
      return "You can contact Sisters for <NAME_EMAIL> or call us at (*************. Our address is 247 East High St, Pottstown, PA 19464. We welcome your questions and ideas - community dialogue helps us grow and better serve our mission.";
    }

    if (message.includes('program') || message.includes('service') || message.includes('offer')) {
      return "Sisters for Good offers various programs including cosmetology education, school partnerships, and community engagement initiatives. Visit our Programs page to learn more. Each program is designed with care to address real needs and create meaningful opportunities for growth and connection.";
    }

    if (message.includes('event') || message.includes('happening') || message.includes('calendar')) {
      return "We host various events throughout the year, including our annual Thanksgiving Luncheon. Check our Events page for upcoming events and how you can participate. Our events are designed to build community, share resources, and celebrate our collective achievements.";
    }

    if (message.includes('mission') || message.includes('purpose') || message.includes('goal')) {
      return "Our mission at Sisters for Good is to empower women through education, mentorship, and community support. We believe that when women are supported and equipped with skills and confidence, entire communities benefit. Our goal is to create a network of support that lifts everyone up.";
    }

    // Default response
    return "I'm not sure I understand your question. Please try rephrasing or visit our Contact page to reach out directly. Remember, I'm here to help you connect with Sisters for Good and our community.";
  }

  addUserMessage(message) {
    if (!this.chatMessages) return;

    const messageElement = document.createElement('div');
    messageElement.className = 'chatbot-message user-message';
    messageElement.innerHTML = `<p>${message}</p>`;
    this.chatMessages.appendChild(messageElement);

    // Scroll to bottom
    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
  }

  addBotMessage(message) {
    if (!this.chatMessages) return;

    const messageElement = document.createElement('div');
    messageElement.className = 'chatbot-message bot-message';
    messageElement.innerHTML = `<p>${message}</p>`;
    this.chatMessages.appendChild(messageElement);

    // Scroll to bottom
    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
  }

  toggleChatbot() {
    if (!this.chatContainer) return;

    this.chatContainer.classList.toggle('chatbot-minimized');
  }

  closeChatbot() {
    if (!this.chatContainer) return;

    // Fully minimize the chatbot
    this.chatContainer.classList.add('chatbot-minimized');
  }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const chatbot = new SFGChatbot();
});
