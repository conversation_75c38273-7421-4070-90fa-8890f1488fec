<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Critical Issues Fix Test - Sisters for Good</title>
  
  <!-- Font Loading Optimizer (prevents header size jumping) -->
  <script src="js/font-loading-optimizer.js"></script>
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">
  
  <style>
    .test-section {
      margin: 2rem 0;
      padding: 2rem;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      background: white;
    }
    
    .status-indicator {
      padding: 0.5rem 1rem;
      border-radius: 4px;
      font-weight: bold;
      text-align: center;
      margin: 1rem 0;
    }
    
    .status-pass { background: #dcfce7; color: #16a34a; }
    .status-fail { background: #fee2e2; color: #dc2626; }
    .status-warning { background: #fef3c7; color: #d97706; }
    
    .test-header {
      background: #f3f4f6;
      padding: 1rem;
      border-radius: 4px;
      margin: 1rem 0;
      position: relative;
    }
    
    .size-monitor {
      position: absolute;
      top: 5px;
      right: 5px;
      background: rgba(0,0,0,0.7);
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 10px;
      font-family: monospace;
    }
  </style>
</head>
<body>
  <!-- Test Header (mimics actual website header) -->
  <header class="bg-white dark:bg-slate-800 shadow-md sticky top-0 z-50 transition-colors duration-300">
    <div class="container mx-auto px-4 py-3">
      <div class="flex justify-between items-center">
        <!-- Logo -->
        <a href="index.html" class="flex items-center">
          <h1 class="text-xl sm:text-2xl font-bold text-pink-600 dark:text-pink-400">Sisters for Good</h1>
        </a>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex space-x-6">
          <a href="#" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">Home</a>
          <a href="#" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">About</a>
          <a href="#" class="nav-link text-gray-700 dark:text-gray-300 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">Programs</a>
        </nav>

        <!-- Theme Toggle Button -->
        <button id="theme-toggle" class="theme-toggle hidden md:flex" aria-label="Toggle dark mode" title="Toggle dark/light theme">
          <svg class="sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
          </svg>
          <svg class="moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
          </svg>
        </button>

        <!-- Mobile menu button -->
        <button id="mobileMenuBtn" class="md:hidden flex items-center p-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-pink-600 dark:text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-4 py-8 max-w-6xl">
    <h1 class="text-4xl font-bold text-center mb-8 text-gray-900 dark:text-white">Critical Issues Fix Test</h1>
    
    <!-- Issue 1: Header Display Glitches -->
    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Issue 1: Header Display Glitches</h2>
      <div id="header-glitch-status" class="status-indicator status-warning">Testing...</div>
      
      <div class="test-header">
        <h1 class="text-2xl font-bold">Test Header 1</h1>
        <div class="size-monitor" id="header1-size">Measuring...</div>
      </div>
      
      <div class="test-header">
        <h2 class="text-xl font-bold">Test Header 2</h2>
        <div class="size-monitor" id="header2-size">Measuring...</div>
      </div>
      
      <div class="test-header">
        <h3 class="text-lg font-bold">Test Header 3</h3>
        <div class="size-monitor" id="header3-size">Measuring...</div>
      </div>
      
      <p class="mt-4 text-sm text-gray-600">Headers should maintain consistent sizing without visual jumping during page load.</p>
    </div>

    <!-- Issue 2: Theme Toggle Functionality -->
    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Issue 2: Theme Toggle Functionality</h2>
      <div id="theme-toggle-status" class="status-indicator status-warning">Testing...</div>
      
      <div class="space-y-4">
        <p><strong>Current Theme:</strong> <span id="current-theme">Loading...</span></p>
        <p><strong>Theme Toggle Button:</strong> <span id="toggle-button-status">Checking...</span></p>
        <p><strong>Theme Persistence:</strong> <span id="theme-persistence">Checking...</span></p>
        
        <button onclick="testThemeToggle()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
          Test Theme Toggle
        </button>
      </div>
    </div>

    <!-- Issue 3: Page Load Performance -->
    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Issue 3: Page Load Performance</h2>
      <div id="performance-status" class="status-indicator status-warning">Testing...</div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Font Loading</h3>
          <div id="font-load-time">Measuring...</div>
        </div>
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Script Execution</h3>
          <div id="script-execution-time">Measuring...</div>
        </div>
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Layout Stability (CLS)</h3>
          <div id="cls-score">Measuring...</div>
        </div>
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Total Load Time</h3>
          <div id="total-load-time">Measuring...</div>
        </div>
      </div>
    </div>

    <!-- Issue 4: Header Layout and Logo Positioning -->
    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Issue 4: Header Layout and Logo Positioning</h2>
      <div id="layout-status" class="status-indicator status-warning">Testing...</div>
      
      <div class="space-y-4">
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Theme Toggle Button Size</h3>
          <div id="toggle-size">Measuring...</div>
        </div>
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Logo Alignment</h3>
          <div id="logo-alignment">Checking...</div>
        </div>
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-bold mb-2">Header Height Consistency</h3>
          <div id="header-height">Measuring...</div>
        </div>
      </div>
    </div>

    <!-- Overall Test Results -->
    <div class="test-section">
      <h2 class="text-2xl font-bold mb-4">Overall Test Results</h2>
      <div id="overall-status" class="status-indicator status-warning">Running tests...</div>
      
      <div id="test-summary" class="mt-4">
        <h3 class="font-bold mb-2">Test Summary:</h3>
        <ul id="test-results-list" class="list-disc list-inside space-y-1">
          <li>Running comprehensive tests...</li>
        </ul>
      </div>
      
      <div class="mt-6">
        <button onclick="runAllTests()" class="bg-green-600 text-white px-6 py-3 rounded hover:bg-green-700">
          Re-run All Tests
        </button>
        <button onclick="clearTestResults()" class="bg-gray-600 text-white px-6 py-3 rounded hover:bg-gray-700 ml-4">
          Clear Results
        </button>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="js/main.js"></script>
  
  <script>
    // Test script for critical issues
    class CriticalIssuesTest {
      constructor() {
        this.startTime = performance.now();
        this.testResults = {
          headerGlitches: false,
          themeToggle: false,
          performance: false,
          layout: false
        };
        this.init();
      }

      init() {
        // Wait for everything to load
        setTimeout(() => {
          this.runAllTests();
        }, 1000);
      }

      runAllTests() {
        this.testHeaderGlitches();
        this.testThemeToggle();
        this.testPerformance();
        this.testLayout();
        this.updateOverallStatus();
      }

      testHeaderGlitches() {
        const headers = [
          { selector: '.test-header h1', id: 'header1-size' },
          { selector: '.test-header h2', id: 'header2-size' },
          { selector: '.test-header h3', id: 'header3-size' }
        ];

        let allStable = true;
        headers.forEach(({ selector, id }) => {
          const element = document.querySelector(selector);
          const monitor = document.getElementById(id);
          
          if (element && monitor) {
            const rect = element.getBoundingClientRect();
            const size = `${Math.round(rect.width)}×${Math.round(rect.height)}`;
            monitor.textContent = size;
            
            // Check if font loading optimizer is working
            if (window.fontLoadingOptimizer) {
              allStable = true;
            } else {
              allStable = false;
            }
          }
        });

        this.testResults.headerGlitches = allStable;
        document.getElementById('header-glitch-status').className = 
          `status-indicator ${allStable ? 'status-pass' : 'status-fail'}`;
        document.getElementById('header-glitch-status').textContent = 
          allStable ? '✅ Headers stable, no glitches detected' : '❌ Header glitches detected';
      }

      testThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        
        document.getElementById('current-theme').textContent = currentTheme;
        
        let toggleWorks = false;
        if (themeToggle) {
          document.getElementById('toggle-button-status').textContent = '✅ Button found';
          
          // Test if click handler is attached
          const hasClickHandler = themeToggle.onclick || 
            themeToggle.addEventListener.toString().includes('click');
          
          if (hasClickHandler || typeof toggleTheme === 'function') {
            toggleWorks = true;
            document.getElementById('toggle-button-status').textContent = '✅ Button functional';
          }
        } else {
          document.getElementById('toggle-button-status').textContent = '❌ Button not found';
        }

        // Test theme persistence
        const savedTheme = localStorage.getItem('sisters-for-good-theme');
        document.getElementById('theme-persistence').textContent = 
          savedTheme ? `✅ Theme saved: ${savedTheme}` : '⚠️ No saved theme';

        this.testResults.themeToggle = toggleWorks;
        document.getElementById('theme-toggle-status').className = 
          `status-indicator ${toggleWorks ? 'status-pass' : 'status-fail'}`;
        document.getElementById('theme-toggle-status').textContent = 
          toggleWorks ? '✅ Theme toggle working correctly' : '❌ Theme toggle not functional';
      }

      testPerformance() {
        const loadTime = performance.now() - this.startTime;
        document.getElementById('total-load-time').textContent = `${Math.round(loadTime)}ms`;

        // Test font loading
        const fontOptimizer = window.fontLoadingOptimizer;
        if (fontOptimizer) {
          document.getElementById('font-load-time').innerHTML = 
            '<span class="text-green-600">✅ Font optimizer active</span>';
        } else {
          document.getElementById('font-load-time').innerHTML = 
            '<span class="text-red-600">❌ Font optimizer not found</span>';
        }

        // Test script execution
        document.getElementById('script-execution-time').textContent = `${Math.round(loadTime)}ms`;

        // Performance score
        const performanceGood = loadTime < 2000 && fontOptimizer;
        this.testResults.performance = performanceGood;
        
        document.getElementById('performance-status').className = 
          `status-indicator ${performanceGood ? 'status-pass' : 'status-fail'}`;
        document.getElementById('performance-status').textContent = 
          performanceGood ? '✅ Performance optimized' : '❌ Performance issues detected';
      }

      testLayout() {
        const themeToggle = document.getElementById('theme-toggle');
        const logo = document.querySelector('header h1');
        const header = document.querySelector('header');

        let layoutGood = true;

        // Test theme toggle size
        if (themeToggle) {
          const toggleRect = themeToggle.getBoundingClientRect();
          const toggleSize = `${Math.round(toggleRect.width)}×${Math.round(toggleRect.height)}`;
          document.getElementById('toggle-size').textContent = toggleSize;
          
          if (toggleRect.width > 50 || toggleRect.height > 50) {
            layoutGood = false;
            document.getElementById('toggle-size').innerHTML += ' <span class="text-red-600">❌ Too large</span>';
          } else {
            document.getElementById('toggle-size').innerHTML += ' <span class="text-green-600">✅ Correct size</span>';
          }
        }

        // Test logo alignment
        if (logo) {
          const logoRect = logo.getBoundingClientRect();
          document.getElementById('logo-alignment').textContent = 
            `✅ Logo positioned at ${Math.round(logoRect.top)}px from top`;
        }

        // Test header height
        if (header) {
          const headerRect = header.getBoundingClientRect();
          document.getElementById('header-height').textContent = 
            `${Math.round(headerRect.height)}px height`;
        }

        this.testResults.layout = layoutGood;
        document.getElementById('layout-status').className = 
          `status-indicator ${layoutGood ? 'status-pass' : 'status-fail'}`;
        document.getElementById('layout-status').textContent = 
          layoutGood ? '✅ Layout properly aligned' : '❌ Layout issues detected';
      }

      updateOverallStatus() {
        const allPassed = Object.values(this.testResults).every(result => result);
        const passedCount = Object.values(this.testResults).filter(result => result).length;
        const totalTests = Object.keys(this.testResults).length;

        document.getElementById('overall-status').className = 
          `status-indicator ${allPassed ? 'status-pass' : 'status-warning'}`;
        document.getElementById('overall-status').textContent = 
          `${passedCount}/${totalTests} tests passed ${allPassed ? '✅' : '⚠️'}`;

        // Update test summary
        const resultsList = document.getElementById('test-results-list');
        resultsList.innerHTML = Object.entries(this.testResults)
          .map(([test, passed]) => 
            `<li>${passed ? '✅' : '❌'} ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}</li>`
          ).join('');
      }
    }

    // Global functions for manual testing
    function testThemeToggle() {
      if (typeof toggleTheme === 'function') {
        toggleTheme();
        setTimeout(() => window.criticalTest.testThemeToggle(), 100);
      } else {
        alert('Theme toggle function not available');
      }
    }

    function runAllTests() {
      window.criticalTest.runAllTests();
    }

    function clearTestResults() {
      location.reload();
    }

    // Initialize test when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        window.criticalTest = new CriticalIssuesTest();
      }, 500);
    });
  </script>
</body>
</html>
