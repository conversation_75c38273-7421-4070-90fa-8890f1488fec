/* Sisters for Good - Main CSS */

/* Font Loading Optimization - Prevent FOUT (Flash of Unstyled Text) */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap');

/* Font Face Declarations with font-display for better loading */
@font-face {
  font-family: 'Poppins';
  font-display: swap;
  src: local('Poppins');
}

@font-face {
  font-family: 'Roboto';
  font-display: swap;
  src: local('Roboto');
}

/* Base Styles - Light Theme */
:root {
  --primary-color: #fc5c7d;
  --secondary-color: #6a82fb;
  --accent-color: #ffee02;
  --text-color: #333333;
  --light-color: #ffffff;
  --dark-color: #2c3e50;
  --gray-color: #f5f5f5;
  --border-radius: 0.375rem;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;

  /* Light Theme Variables */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --border-color: #e2e8f0;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --primary-color: #ff6b9d;
  --secondary-color: #7c3aed;
  --accent-color: #fbbf24;
  --text-color: #e2e8f0;
  --light-color: #1e293b;
  --dark-color: #f1f5f9;
  --gray-color: #334155;

  /* Dark Theme Variables */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* Prevent layout shifts and ensure smooth loading */
html {
  scroll-behavior: smooth;
  font-size: 16px; /* Base font size to prevent scaling issues */
}

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Prevent font loading layout shifts */
  font-size-adjust: 0.5;
}

/* Prevent FOUT (Flash of Unstyled Text) */
.fonts-loading body {
  visibility: hidden;
}

.fonts-loaded body {
  visibility: visible;
}

/* Ensure consistent sizing during font loading */
* {
  box-sizing: border-box;
}

/* Prevent layout shift for images */
img {
  max-width: 100%;
  height: auto;
  font-style: italic;
  background-repeat: no-repeat;
  background-size: cover;
  shape-margin: 0.75rem;
}

/* Typography - Prevent Header Size Jumping */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-display: swap;
  /* Prevent layout shift during font loading */
  font-size-adjust: 0.5;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Specific header sizing to prevent jumping */
h1 {
  font-size: clamp(1.75rem, 4vw, 3rem);
  min-height: 1.2em;
}

h2 {
  font-size: clamp(1.5rem, 3.5vw, 2.5rem);
  min-height: 1.2em;
}

h3 {
  font-size: clamp(1.25rem, 3vw, 2rem);
  min-height: 1.2em;
}

/* Header-specific sizing fixes */
header h1 {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem) !important;
  min-height: 1.5em;
  line-height: 1.2;
  font-weight: 700;
}

/* Hero section header fixes */
.hero h1,
section h1 {
  font-size: clamp(2rem, 5vw, 4rem);
  min-height: 1.2em;
  line-height: 1.1;
}

/* Page title headers */
.text-4xl,
.text-5xl {
  font-size: clamp(2rem, 4vw, 3rem) !important;
  min-height: 1.2em;
  line-height: 1.1;
}

.text-xl {
  font-size: clamp(1.125rem, 2vw, 1.25rem) !important;
  min-height: 1.2em;
}

.text-2xl {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem) !important;
  min-height: 1.2em;
}

.text-3xl {
  font-size: clamp(1.5rem, 3vw, 1.875rem) !important;
  min-height: 1.2em;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--secondary-color);
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section {
  padding: 4rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  font-size: 2.5rem;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

.section-title h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

/* Navigation */
.nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 0.5rem;
  transition: var(--transition);
}

.nav-link:hover, .nav-link.active {
  color: var(--primary-color);
}



/* Buttons */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-align: center;
  transition: var(--transition);
  cursor: pointer;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--light-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  color: var(--light-color);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--light-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: var(--light-color);
}

.btn-accent {
  background-color: var(--accent-color);
  color: var(--dark-color);
}

.btn-accent:hover {
  background-color: #e5d500;
  color: var(--dark-color);
}

/* Cards */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
}

.card-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-body {
  padding: 1.5rem;
  color: var(--text-primary);
}

.card-title {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

/* Hero Section */
.hero {
  position: relative;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--light-color);
  padding: 6rem 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.hero p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
}

/* Dark Theme Toggle Button */
.theme-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--bg-secondary);
  border: 2px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-light);
}

.theme-toggle:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-medium);
  transform: scale(1.05);
}

.theme-toggle svg {
  width: 20px;
  height: 20px;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.theme-toggle .sun-icon {
  opacity: 1;
  transform: rotate(0deg);
}

.theme-toggle .moon-icon {
  opacity: 0;
  transform: rotate(180deg);
  position: absolute;
}

[data-theme="dark"] .theme-toggle .sun-icon {
  opacity: 0;
  transform: rotate(180deg);
}

[data-theme="dark"] .theme-toggle .moon-icon {
  opacity: 1;
  transform: rotate(0deg);
}

/* Base Colors */
:root {
  --text-color: #333333;
  --light-color: #ffffff;
  --gray-color: #f5f5f5;
}

/* Dark Theme Specific Styles */
[data-theme="dark"] .bg-white {
  background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .bg-gray-50 {
  background-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .bg-gray-100 {
  background-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .text-gray-600 {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .text-gray-700 {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .text-gray-800 {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .text-gray-900 {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .border-gray-200 {
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .border-gray-300 {
  border-color: var(--border-color) !important;
}

/* Form Elements Dark Theme */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 2.5rem;
  }

  .section-title h2 {
    font-size: 2rem;
  }

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .card {
    margin-bottom: 1.5rem;
  }

  .theme-toggle {
    width: 40px;
    height: 40px;
  }

  .theme-toggle svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 576px) {
  .hero h1 {
    font-size: 2rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .hero p {
    font-size: 1.1rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section-title h2 {
    font-size: 1.75rem;
  }

  .section {
    padding: 2.5rem 0;
  }

  .card-body {
    padding: 1.25rem;
  }

  .btn {
    padding: 0.6rem 1.2rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 1s ease-in-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp 0.5s ease-in-out;
}



/* Testimonials Carousel */
.testimonials-carousel {
  position: relative;
  overflow: hidden;
}

.testimonial-slides {
  position: relative;
  height: 100%;
}

.testimonial-slide {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.testimonial-slide.active {
  display: block;
  opacity: 1;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ccc;
  display: inline-block;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.carousel-dot.active {
  background-color: var(--primary-color);
  transform: scale(1.2);
}

/* Google Form */
.google-form-container {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.google-form-wrapper {
  height: 700px;
  overflow: hidden;
}

.google-form-wrapper iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

@media (max-width: 768px) {
  .google-form-wrapper {
    height: 600px;
  }
}

/* Utilities */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-5 {
  margin-top: 3rem;
}

/* Accessibility Styles */
.skip-links {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}

.skip-link {
  position: absolute;
  top: -9999px;
  left: -9999px;
  background: #fc5c7d;
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 600;
  font-size: 14px;
  white-space: nowrap;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid #ffffff;
  transition: all 0.3s ease;
}

.skip-link:focus {
  position: fixed;
  top: 10px;
  left: 10px;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  z-index: 10000;
  outline: 2px solid #ffffff;
  outline-offset: 2px;
  transform: translateY(0);
  opacity: 1;
}

/* Ensure skip links work in high contrast mode */
@media (prefers-contrast: high) {
  .skip-link:focus {
    background: #000000;
    color: #ffffff;
    border: 3px solid #ffffff;
  }
}

/* Ensure skip links are accessible in reduced motion mode */
@media (prefers-reduced-motion: reduce) {
  .skip-link {
    transition: none;
  }
}

/* Additional skip link improvements */
.skip-links:focus-within {
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
}

/* Ensure skip links work with keyboard navigation */
.skip-link:focus-visible {
  position: fixed;
  top: 10px;
  left: 10px;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  z-index: 10000;
}

/* Improve skip link spacing when multiple are focused */
.skip-link:focus + .skip-link:focus {
  left: 200px;
}

/* Dark mode support for skip links */
.dark .skip-link:focus {
  background: #fc5c7d;
  color: white;
  border-color: #ffffff;
}

/* Ensure skip links work in all browsers */
.skip-link[href]:focus {
  position: fixed !important;
  top: 10px !important;
  left: 10px !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  clip: auto !important;
  z-index: 10000 !important;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
*:focus {
  outline: 2px solid #fc5c7d;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }

  .card {
    border: 1px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus visible for better keyboard navigation */
.focus-visible:focus {
  outline: 2px solid #fc5c7d;
  outline-offset: 2px;
}

/* Performance Optimizations */
/* Lazy loading styles */
.lazy-loading {
  opacity: 0;
  transition: opacity 0.3s ease;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.lazy-loaded {
  opacity: 1;
}

.lazy-error {
  opacity: 0.5;
  background: #f5f5f5;
  position: relative;
}

.lazy-error::after {
  content: "⚠️ Failed to load";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.875rem;
  color: #666;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Critical CSS for above-the-fold content */
.hero {
  contain: layout style paint;
}

.card {
  contain: layout style;
}

/* GPU acceleration for animations */
.theme-toggle,
.btn,
.card {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimize font loading */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Poppins Regular'), local('Poppins-Regular');
}

/* Reduce layout shifts */
img {
  height: auto;
  max-width: 100%;
}

/* Optimize animations for performance */
@media (prefers-reduced-motion: no-preference) {
  .slide-in-up {
    transform: translate3d(0, 0, 0);
  }
}

/* Critical resource hints */
.preload-hint {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}
