/* Sisters for Good - Main CSS */

/* Base Styles */
:root {
  --primary-color: #fc5c7d;
  --secondary-color: #6a82fb;
  --accent-color: #ffee02;
  --text-color: #333333;
  --light-color: #ffffff;
  --dark-color: #2c3e50;
  --gray-color: #f5f5f5;
  --border-radius: 0.375rem;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Poppins', sans-serif;
  color: var(--text-color);
  line-height: 1.6;
  background-color: var(--light-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--secondary-color);
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section {
  padding: 4rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  font-size: 2.5rem;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

.section-title h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

/* Navigation */
.nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 0.5rem;
  transition: var(--transition);
}

.nav-link:hover, .nav-link.active {
  color: var(--primary-color);
}



/* Buttons */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-align: center;
  transition: var(--transition);
  cursor: pointer;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--light-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  color: var(--light-color);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--light-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: var(--light-color);
}

.btn-accent {
  background-color: var(--accent-color);
  color: var(--dark-color);
}

.btn-accent:hover {
  background-color: #e5d500;
  color: var(--dark-color);
}

/* Cards */
.card {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-body {
  padding: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
}

/* Hero Section */
.hero {
  position: relative;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--light-color);
  padding: 6rem 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.hero p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
}

/* Base Colors */
:root {
  --text-color: #333333;
  --light-color: #ffffff;
  --gray-color: #f5f5f5;
}

/* Responsive */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 2.5rem;
  }

  .section-title h2 {
    font-size: 2rem;
  }

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .card {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 576px) {
  .hero h1 {
    font-size: 2rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .hero p {
    font-size: 1.1rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .section-title h2 {
    font-size: 1.75rem;
  }

  .section {
    padding: 2.5rem 0;
  }

  .card-body {
    padding: 1.25rem;
  }

  .btn {
    padding: 0.6rem 1.2rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 1s ease-in-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp 0.5s ease-in-out;
}

/* Chatbot Styles */
.chatbot-message {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 10px;
  max-width: 80%;
  word-wrap: break-word;
}

.user-message {
  background-color: #f0f0f0;
  margin-left: auto;
  border-top-right-radius: 0;
}

.bot-message {
  background-color: var(--primary-color);
  color: white;
  margin-right: auto;
  border-top-left-radius: 0;
}

.chatbot-minimized .p-4 {
  display: none;
}

.chatbot-minimized #chatbot-toggle span {
  transform: rotate(180deg);
  display: inline-block;
}

.chatbot-close {
  cursor: pointer;
  font-size: 1.5rem;
  line-height: 1;
  transition: var(--transition);
}

.chatbot-close:hover {
  opacity: 0.7;
}

/* Testimonials Carousel */
.testimonials-carousel {
  position: relative;
  overflow: hidden;
}

.testimonial-slides {
  position: relative;
  height: 100%;
}

.testimonial-slide {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.testimonial-slide.active {
  display: block;
  opacity: 1;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ccc;
  display: inline-block;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.carousel-dot.active {
  background-color: var(--primary-color);
  transform: scale(1.2);
}

/* Google Form */
.google-form-container {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.google-form-wrapper {
  height: 700px;
  overflow: hidden;
}

.google-form-wrapper iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

@media (max-width: 768px) {
  .google-form-wrapper {
    height: 600px;
  }
}

/* Utilities */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-5 {
  margin-top: 3rem;
}
