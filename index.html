<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Sisters for Good - Empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach.">
  <title>Sisters for Good | Home</title>

  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-XXXXXXXXXX');
  </script>

  <!-- Favicon -->
  <link rel="icon" href="favicon.ico" type="image/x-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Alpine.js -->
  <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#fc5c7d',
            secondary: '#6a82fb',
            accent: '#ffee02'
          },
          fontFamily: {
            sans: ['Poppins', 'sans-serif'],
            body: ['Roboto', 'sans-serif']
          }
        }
      }
    }
  </script>
</head>
<body>

<!-- Header Component -->
<header class="bg-white shadow-md sticky top-0 z-50">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <a href="index.html" class="flex items-center">
        <h1 class="text-xl sm:text-2xl font-bold text-pink-600">Sisters for Good</h1>
      </a>

      <!-- Desktop Navigation (hidden on mobile) -->
      <nav class="hidden md:flex space-x-6">
        <a href="index.html" class="nav-link text-pink-600 font-semibold">Home</a>
        <a href="about.html" class="nav-link hover:text-pink-600 transition-colors">About Us</a>
        <a href="programs.html" class="nav-link hover:text-pink-600 transition-colors">Programs</a>
        <a href="events.html" class="nav-link hover:text-pink-600 transition-colors">Events</a>
        <a href="health.html" class="nav-link hover:text-pink-600 transition-colors">Health Resources</a>
        <a href="donate.html" class="nav-link hover:text-pink-600 transition-colors">Donate</a>
        <a href="contact.html" class="nav-link hover:text-pink-600 transition-colors">Contact</a>
      </nav>

      <!-- Mobile menu button -->
      <button id="mobileMenuBtn" class="md:hidden flex items-center p-3 rounded-md hover:bg-gray-100 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation (hidden by default) -->
    <div id="mobileMenu" class="md:hidden hidden mt-4 pb-4 border-t border-gray-200 pt-3">
      <nav class="flex flex-col space-y-4">
        <a href="index.html" class="nav-link text-center py-2 bg-pink-50 text-pink-600 font-medium rounded-md">Home</a>
        <a href="about.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">About Us</a>
        <a href="programs.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Programs</a>
        <a href="events.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Events</a>
        <a href="health.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Health Resources</a>
        <a href="donate.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Donate</a>
        <a href="contact.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Contact</a>
      </nav>
    </div>
  </div>
</header>

<!-- Hero Section Carousel -->
<section class="w-full bg-gradient-to-r from-pink-400 to-purple-500 text-white py-8 md:py-12 px-4 md:px-6 text-center relative overflow-hidden mb-8 md:mb-12"
         x-data="{
           slide: 0,
           banners: [
             {
               title: '💪 Decade of Sisterhood & Strength',
               desc: 'Ten years strong! Join our movement of Black women building generational wealth, breaking barriers, and creating lasting change in our communities.',
               image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?fm=jpg&q=80&w=1200&ixlib=rb-4.0.3',
               imageAlt: 'Powerful Black women leaders celebrating community achievements',
               badge: '10 Years Strong',
               buttons: [
                 { text: 'Our Impact Story', link: 'about.html', style: 'bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-gray-900 font-bold shadow-xl' },
                 { text: 'Support Our Mission', link: 'donate.html', style: 'bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-bold shadow-xl' }
               ]
             },
             {
               title: '🌬️ Breathe Free, Live Strong',
               desc: 'World No Tobacco Day 2025: Sisters for Good supports tobacco-free living and healthy communities. Learn about resources and support available for tobacco cessation.',
               image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?fm=jpg&q=80&w=1200&ixlib=rb-4.0.3',
               imageAlt: 'Confident Black woman embracing healthy lifestyle and wellness',
               badge: 'May 31st',
               buttons: [
                 { text: 'Learn More', link: 'https://www.who.int/campaigns/world-no-tobacco-day', style: 'bg-gradient-to-r from-blue-500 to-teal-500 hover:from-blue-600 hover:to-teal-600 text-white font-bold shadow-xl' },
                 { text: 'Health Resources', link: 'health.html', style: 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold shadow-xl' }
               ]
             },
             {
               title: '🍳 Father\'s Day Appreciation Brunch',
               desc: 'Sunday, June 15th • 10:00 AM - 11:00 AM\\nCelebrating the kings who show up every day! Join us for a special brunch honoring fathers, father figures, and male mentors who strengthen our community.',
               image: 'assets/fathers_day_up_coming.png',
               imageAlt: 'Father\'s Day Appreciation Brunch event promotional image',
               badge: 'Special Event',
               eventDetails: {
                 date: 'Sunday, June 15th',
                 time: '10:00 AM - 11:00 AM',
                 location: '247 East High St, Pottstown, PA',
                 highlights: ['Complimentary Brunch', 'Father Appreciation Ceremony', 'Community Fellowship', 'Special Gifts for Dads']
               },
               buttons: [
                 { text: 'RSVP Now', link: '#fathers-day-rsvp', style: 'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold shadow-xl animate-pulse' },
                 { text: 'Download Flyer', link: '#fathers-day-flyer', style: 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold shadow-xl' }
               ]
             }
           ]
         }"
         x-init="setInterval(() => slide = (slide + 1) % banners.length, 7000)">

  <!-- Background overlay for better text readability -->
  <div class="absolute inset-0 bg-black/30"></div>

  <div class="max-w-6xl mx-auto relative z-10 min-h-[500px] md:min-h-[400px] flex items-center py-4">
    <template x-for="(banner, index) in banners" :key="index">
      <div x-show="slide === index"
           x-transition:enter="transition ease-in-out duration-700"
           x-transition:enter-start="opacity-0 transform translate-x-4"
           x-transition:enter-end="opacity-100 transform translate-x-0"
           x-transition:leave="transition ease-in-out duration-700"
           x-transition:leave-start="opacity-100 transform translate-x-0"
           x-transition:leave-end="opacity-0 transform -translate-x-4"
           class="w-full grid md:grid-cols-2 gap-6 md:gap-8 items-center absolute inset-0 px-2 md:px-0">

        <!-- Image Section - Left Side -->
        <div class="order-2 md:order-1 flex justify-center relative">
          <div class="relative">
            <img :src="banner.image"
                 :alt="banner.imageAlt"
                 @error="$event.target.src='https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?fm=jpg&q=80&w=1200&ixlib=rb-4.0.3'"
                 class="w-40 h-40 sm:w-48 sm:h-48 md:w-64 md:h-64 rounded-2xl object-cover shadow-2xl border-4 border-white transform hover:scale-105 transition-transform duration-300">
            <!-- Badge Overlay -->
            <div x-show="banner.badge"
                 class="absolute -top-3 -right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 px-4 py-2 rounded-full text-sm font-bold shadow-lg transform rotate-12 animate-bounce">
              <span x-text="banner.badge"></span>
            </div>
          </div>
        </div>

        <!-- Content Section - Right Side -->
        <div class="order-1 md:order-2 text-center md:text-left px-2 md:px-0">
          <h1 class="text-xl sm:text-2xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4 text-white drop-shadow-lg leading-tight" x-text="banner.title"></h1>

          <!-- Regular Description -->
          <div x-show="!banner.eventDetails">
            <p class="text-sm sm:text-base md:text-lg lg:text-xl mb-4 md:mb-6 text-white drop-shadow-md leading-relaxed whitespace-pre-line" x-text="banner.desc"></p>
          </div>

          <!-- Special Event Details for Father's Day -->
          <div x-show="banner.eventDetails" class="mb-6 md:mb-8">
            <p class="text-sm sm:text-base md:text-lg mb-3 md:mb-4 text-white drop-shadow-md leading-relaxed whitespace-pre-line" x-text="banner.desc"></p>

            <!-- Event Info Card -->
            <div class="bg-white/20 backdrop-blur-sm rounded-xl p-3 md:p-4 mb-3 md:mb-4 border border-white/30">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 text-left">
                <div>
                  <h3 class="font-bold text-yellow-300 mb-2 text-sm md:text-base">📅 Event Details</h3>
                  <p class="text-white mb-1 text-xs md:text-sm"><strong>Date:</strong> <span x-text="banner.eventDetails?.date"></span></p>
                  <p class="text-white mb-1 text-xs md:text-sm"><strong>Time:</strong> <span x-text="banner.eventDetails?.time"></span></p>
                  <p class="text-white text-xs md:text-sm"><strong>Location:</strong> <span x-text="banner.eventDetails?.location"></span></p>
                </div>
                <div>
                  <h3 class="font-bold text-yellow-300 mb-2 text-sm md:text-base">🎉 What to Expect</h3>
                  <template x-for="highlight in banner.eventDetails?.highlights || []" :key="highlight">
                    <p class="text-white text-xs md:text-sm mb-1">• <span x-text="highlight"></span></p>
                  </template>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-center md:justify-start gap-2 md:gap-4 flex-wrap">
            <template x-for="btn in banner.buttons" :key="btn.text">
              <a :href="btn.link"
                 class="py-2 px-4 md:py-3 md:px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 text-sm md:text-base font-semibold"
                 :class="btn.style"
                 x-text="btn.text"></a>
            </template>
          </div>
        </div>
      </div>
    </template>
  </div>

  <!-- Carousel Indicators - Moved outside and lower -->
  <div class="absolute -bottom-8 md:-bottom-12 left-1/2 transform -translate-x-1/2 flex space-x-3 md:space-x-4 z-20">
    <template x-for="(banner, index) in banners" :key="index">
      <button @click="slide = index"
              class="w-5 h-5 rounded-full transition-all duration-300 border-2 border-white hover:scale-110"
              :class="slide === index ? 'bg-white shadow-lg' : 'bg-white/40 hover:bg-white/60'"></button>
    </template>
  </div>
</section>

<!-- Featured Programs Section -->
<section class="section bg-gray-100">
  <div class="container">
    <div class="section-title">
      <h2 data-aos="fade-up">Our Programs</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Program 1 -->
      <div class="card" data-aos="fade-up">
        <img src="https://images.unsplash.com/photo-1621973856220-29115d9b5d29?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8d29tZW4lMjBlbXBvd2VybWVudHxlbnwwfHwwfHx8MA%3D%3D" alt="Cosmetology Education" class="card-img">
        <div class="card-body">
          <h3 class="card-title">Cosmetology Education</h3>
          <p>Teaching high school students the art and science of hair care and beauty—opening doors to future careers.</p>
          <a href="programs.html#cosmetology" class="btn btn-primary mt-4">Learn More</a>
        </div>
      </div>

      <!-- Program 2 -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <img src="https://images.unsplash.com/photo-1636987050384-9b079c700f63?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8d29tZW4lMjBlbXBvd2VybWVudHxlbnwwfHwwfHx8MA%3D%3D" alt="School Partnerships" class="card-img">
        <div class="card-body">
          <h3 class="card-title">School Partnerships</h3>
          <p>Collaborating with local schools to provide mentorship, resources, and support for young women.</p>
          <a href="programs.html#partnerships" class="btn btn-primary mt-4">Learn More</a>
        </div>
      </div>

      <!-- Program 3 -->
      <div class="card" data-aos="fade-up" data-aos-delay="200">
        <img src="https://images.unsplash.com/photo-1636986905406-758b0e280f49?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8d29tZW4lMjBlbXBvd2VybWVudHxlbnwwfHwwfHx8MA%3D%3D" alt="Community Engagement" class="card-img">
        <div class="card-body">
          <h3 class="card-title">Community Engagement</h3>
          <p>Creating spaces for connection, growth, and mutual support within our communities.</p>
          <a href="programs.html#community" class="btn btn-primary mt-4">Learn More</a>
        </div>
      </div>
    </div>
    <div class="text-center mt-8">
      <a href="programs.html" class="btn btn-secondary">View All Programs</a>
    </div>
  </div>
</section>

<!-- Inspiration Section -->
<section class="section bg-gray-100 text-center">
  <div class="container">
    <div class="section-title">
      <h2 data-aos="fade-up">Daily Inspiration</h2>
    </div>
    <div class="max-w-2xl mx-auto">
      <p id="quoteText" class="text-xl italic mb-2" data-aos="fade-up">Loading inspiration...</p>
      <p id="quoteAuthor" class="text-lg font-bold" data-aos="fade-up" data-aos-delay="100"></p>
    </div>
  </div>
</section>

<!-- About Us Section -->
<section class="section">
  <div class="container">
    <div class="section-title">
      <h2 data-aos="fade-up">About Us</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
      <div data-aos="fade-right">
        <img src="assets/sfg_food_20xx.png" alt="Sisters for Good volunteers serving the Black community during food distribution event, demonstrating our commitment to community service and support" class="rounded-lg shadow-lg w-full">
      </div>
      <div data-aos="fade-left">
        <p class="mb-4">Sisters for Good was founded over 10 years ago with a simple yet powerful vision: to create a supportive community where women could thrive, learn, and grow together.</p>
        <p class="mb-4">Our mission is to create sustainable programs that empower women economically, emotionally, and professionally while building strong community bonds.</p>
        <div class="mt-6">
          <a href="about.html" class="btn btn-primary">Learn More About Us</a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Upcoming Events Section -->
<section class="section bg-gray-100">
  <div class="container">
    <div class="section-title">
      <h2 data-aos="fade-up">Upcoming Events</h2>
    </div>

    <!-- Featured Event -->
    <div class="card mb-8" data-aos="fade-up">
      <div class="md:flex">
        <div class="md:w-1/3">
          <img src="assets/fathers_day_up_coming.png" alt="Father's Day Brunch" class="w-full h-full object-cover">
        </div>
        <div class="md:w-2/3 p-6">
          <h3 class="text-2xl font-bold mb-2">Father's Day Brunch</h3>
          <p class="mb-4">Join us for a special Father's Day celebration honoring the fathers and father figures in our community, hosted by Sisters for Good Foundation.</p>
          <div class="flex flex-wrap mb-4">
            <div class="w-full md:w-1/2 mb-2">
              <strong>Date:</strong> Father's Day (Third Sunday in June)
            </div>
            <div class="w-full md:w-1/2 mb-2">
              <strong>Time:</strong> 10:00 AM - 11:00 AM
            </div>
            <div class="w-full mb-2">
              <strong>Location:</strong> 247E High Street, Pottstown PA, 19464
            </div>
          </div>
          <p class="mb-4">Come celebrate and honor the important father figures who strengthen our community and families.</p>
          <a href="events.html#fathers-day" class="btn btn-primary">Learn More</a>
        </div>
      </div>
    </div>

    <div class="text-center">
      <a href="events.html" class="btn btn-secondary">View All Events</a>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="section">
  <div class="container">
    <div class="section-title">
      <h2 data-aos="fade-up">What People Say</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="card" data-aos="fade-up">
        <div class="card-body">
          <div class="text-primary text-4xl mb-4">❝</div>
          <p class="italic mb-4">"Sisters for Good changed my life. The cosmetology program gave me skills and confidence I never knew I had."</p>
          <div class="font-bold">— Jasmine T., Program Graduate</div>
        </div>
      </div>
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="card-body">
          <div class="text-primary text-4xl mb-4">❝</div>
          <p class="italic mb-4">"The mentorship I received helped me navigate college applications and secure a scholarship."</p>
          <div class="font-bold">— Maria L., Mentee</div>
        </div>
      </div>
      <div class="card" data-aos="fade-up" data-aos-delay="200">
        <div class="card-body">
          <div class="text-primary text-4xl mb-4">❝</div>
          <p class="italic mb-4">"As a volunteer, I've seen firsthand the incredible impact this organization has on our community."</p>
          <div class="font-bold">— Sarah K., Volunteer</div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Community Impact Section -->
<section class="section bg-gray-100">
  <div class="container">
    <div class="section-title">
      <h2 data-aos="fade-up">Our Community Impact</h2>
      <p class="text-center max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="100">
        Over the years, Sisters for Good has organized numerous community service events,
        providing essential services and support to families throughout Pottstown and surrounding areas.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Back to School Hair Event -->
      <div class="card" data-aos="fade-up">
        <img src="assets/sfg_back_to_school_20xx.png"
             alt="Black children receiving free haircuts and styling from local barbers and stylists at Sisters for Good back to school event"
             class="card-img">
        <div class="card-body">
          <h3 class="card-title">Back to School Hair Event</h3>
          <p class="mb-4">
            Sisters for Good organized local barbers and hair stylists to provide free haircuts and styling
            for children preparing for the school year, ensuring every child starts school feeling confident and ready to learn.
          </p>
          <div class="flex items-center text-sm text-gray-600">
            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full">Community Service</span>
          </div>
        </div>
      </div>

      <!-- Thanksgiving Food Drive -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <img src="assets/sfg_food_20xx.png"
             alt="Sisters for Good volunteers distributing free Thanksgiving meals and winter coats to Black families and homeless community members"
             class="card-img">
        <div class="card-body">
          <h3 class="card-title">Thanksgiving Food & Coat Drive</h3>
          <p class="mb-4">
            Free Thanksgiving food distribution combined with coat and jacket drive for homeless individuals
            and families in need, providing both nourishment and warmth during the holiday season.
          </p>
          <div class="flex items-center text-sm text-gray-600">
            <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full">Holiday Outreach</span>
          </div>
        </div>
      </div>

      <!-- Community Food Distribution -->
      <div class="card" data-aos="fade-up" data-aos-delay="200">
        <img src="assets/sfg_food_20xx_2.png"
             alt="Black community members receiving food assistance and support from Sisters for Good volunteers during community food distribution event"
             class="card-img">
        <div class="card-body">
          <h3 class="card-title">Community Food Distribution</h3>
          <p class="mb-4">
            Regular food distribution events ensuring that families in our community have access to
            nutritious meals and essential supplies, strengthening our neighborhood bonds through service.
          </p>
          <div class="flex items-center text-sm text-gray-600">
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full">Food Security</span>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-8">
      <p class="text-lg mb-4" data-aos="fade-up">
        These events represent just a few examples of how Sisters for Good continues to serve and uplift our community.
      </p>
      <a href="events.html" class="btn btn-primary" data-aos="fade-up" data-aos-delay="100">View All Past Events</a>
    </div>
  </div>
</section>

<!-- News Section -->
<section class="section bg-gray-100">
  <div class="container">
    <div class="section-title">
      <h2 data-aos="fade-up">Women's Empowerment News</h2>
    </div>
    <div class="card p-6" data-aos="fade-up">
      <ul id="empowermentNewsList" class="space-y-3">
        <li>Loading empowerment stories...</li>
      </ul>
    </div>
  </div>
</section>

<!-- Donation CTA Section -->
<section class="section bg-primary text-white text-center">
  <div class="container">
    <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Support Our Mission</h2>
    <p class="max-w-2xl mx-auto mb-8" data-aos="fade-up" data-aos-delay="100">Your contribution helps us continue our work empowering women and strengthening communities. Every donation makes a difference.</p>
    <a href="donate.html" class="btn btn-accent text-dark-color" data-aos="fade-up" data-aos-delay="200">Donate Now</a>
  </div>
</section>

<!-- Newsletter Section -->
<section class="section">
  <div class="container mx-auto px-4">
    <div id="newsletter-container" data-aos="fade-up">
      <!-- Newsletter component will be loaded here -->
    </div>
  </div>
</section>

<!-- Footer Component -->
<footer class="bg-gray-800 text-white py-10">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Organization Info -->
      <div>
        <h3 class="text-xl font-bold mb-4">Sisters for Good</h3>
        <p class="mb-2">Empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach.</p>
        <p class="text-sm mt-4">&copy; 2025 Sisters for Good. All Rights Reserved.</p>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-xl font-bold mb-4">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="index.html" class="hover:text-pink-400 transition-colors">Home</a></li>
          <li><a href="about.html" class="hover:text-pink-400 transition-colors">About Us</a></li>
          <li><a href="programs.html" class="hover:text-pink-400 transition-colors">Programs</a></li>
          <li><a href="events.html" class="hover:text-pink-400 transition-colors">Events</a></li>
          <li><a href="health.html" class="hover:text-pink-400 transition-colors">Health Resources</a></li>
          <li><a href="donate.html" class="hover:text-pink-400 transition-colors">Donate</a></li>
          <li><a href="contact.html" class="hover:text-pink-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Information -->
      <div>
        <h3 class="text-xl font-bold mb-4">Contact Us</h3>
        <address class="not-italic">
          <p class="mb-2">📍 247 East High St, Pottstown, PA 19464</p>
          <p class="mb-2">📞 (201) 403-7417</p>
          <p class="mb-2">📧 <a href="mailto:<EMAIL>" class="hover:text-pink-400 transition-colors"><EMAIL></a></p>
        </address>

        <!-- Social Media Links -->
        <div class="mt-4 flex space-x-4">
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Facebook</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Instagram</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Twitter</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>



<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>

<!-- Quote and News Scripts -->
<script>
  // Load inspirational quote
  fetch("https://news-api-flask-tmit.onrender.com/api/inspiration")
    .then(res => res.json())
    .then(data => {
      document.getElementById("quoteText").innerText = `"${data.quote}"`;
      document.getElementById("quoteAuthor").innerText = `– ${data.author}`;
    })
    .catch(() => {
      document.getElementById("quoteText").innerText = "Believe in yourself and all that you are. Know that there is something inside you that is greater than any obstacle.";
      document.getElementById("quoteAuthor").innerText = "– Christian D. Larson";
    });

  // Load empowerment news
  fetch("https://news-api-flask-tmit.onrender.com/api/womens-empowerment")
    .then(res => res.json())
    .then(data => {
      const list = document.getElementById("empowermentNewsList");
      list.innerHTML = "";
      data.forEach(article => {
        const item = document.createElement("li");
        item.innerHTML = `<a href="${article.url}" target="_blank" class="hover:text-primary transition-colors">${article.title}</a>`;
        list.appendChild(item);
      });
    })
    .catch(() => {
      document.getElementById("empowermentNewsList").innerHTML = "<li>Could not load empowerment news. Please check back later.</li>";
    });
</script>

<!-- Initialize Scripts -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile menu
    initMobileMenu();

    // Set active nav link
    setActiveNavLink();
  });
</script>

</body>
</html>
