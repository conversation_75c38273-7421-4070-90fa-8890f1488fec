<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Explore the programs and services offered by Sisters for Good, including cosmetology education, school partnerships, and community engagement initiatives.">
  <title>Sisters for Good | Programs</title>

  <!-- Favicon -->
  <link rel="icon" href="favicon.ico" type="image/x-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#fc5c7d',
            secondary: '#6a82fb',
            accent: '#ffee02'
          },
          fontFamily: {
            sans: ['Poppins', 'sans-serif'],
            body: ['Roboto', 'sans-serif']
          }
        }
      }
    }
  </script>
</head>
<body>

<!-- Header Component -->
<header class="bg-white shadow-md sticky top-0 z-50">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <a href="index.html" class="flex items-center">
        <h1 class="text-xl sm:text-2xl font-bold text-pink-600">Sisters for Good</h1>
      </a>

      <!-- Desktop Navigation (hidden on mobile) -->
      <nav class="hidden md:flex space-x-6">
        <a href="index.html" class="nav-link hover:text-pink-600 transition-colors">Home</a>
        <a href="about.html" class="nav-link hover:text-pink-600 transition-colors">About Us</a>
        <a href="programs.html" class="nav-link hover:text-pink-600 transition-colors font-medium">Programs</a>
        <a href="events.html" class="nav-link hover:text-pink-600 transition-colors">Events</a>
        <a href="donate.html" class="nav-link hover:text-pink-600 transition-colors">Donate</a>
        <a href="contact.html" class="nav-link hover:text-pink-600 transition-colors">Contact</a>
      </nav>

      <!-- Mobile menu button -->
      <button id="mobileMenuBtn" class="md:hidden flex items-center p-3 rounded-md hover:bg-gray-100 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation (hidden by default) -->
    <div id="mobileMenu" class="md:hidden hidden mt-4 pb-4 border-t border-gray-200 pt-3">
      <nav class="flex flex-col space-y-4">
        <a href="index.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Home</a>
        <a href="about.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">About Us</a>
        <a href="programs.html" class="nav-link text-center py-2 bg-pink-50 text-pink-600 font-medium rounded-md">Programs</a>
        <a href="events.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Events</a>
        <a href="donate.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Donate</a>
        <a href="contact.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Contact</a>
      </nav>
    </div>
  </div>
</header>

<!-- Page Header -->
<section class="bg-primary text-white py-16">
  <div class="container mx-auto px-4 text-center">
    <h1 class="text-4xl font-bold mb-4" data-aos="fade-up">Our Programs</h1>
    <p class="text-xl max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="100">From classrooms to community centers, Sisters for Good is dedicated to nurturing potential and delivering purposeful education and care.</p>
  </div>
</section>

<!-- Programs Overview -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="max-w-3xl mx-auto text-center mb-12">
      <p data-aos="fade-up">At Sisters for Good, we believe in the power of education, mentorship, and community support to transform lives. Our programs are designed to address the unique challenges faced by women and provide the tools, resources, and support needed to overcome them.</p>
    </div>
  </div>
</section>

<!-- Cosmetology Education Program -->
<section id="cosmetology" class="section bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
      <div data-aos="fade-right">
        <img src="https://images.unsplash.com/photo-1562322140-8baeececf3df?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8aGFpciUyMHNhbG9ufGVufDB8fDB8fHww" alt="Cosmetology Education" class="rounded-lg shadow-lg w-full">
      </div>
      <div data-aos="fade-left">
        <h2 class="text-3xl font-bold mb-4">Cosmetology Education</h2>
        <p class="mb-4">Our flagship program teaches high school students the art and science of hair care and beauty, opening doors to future careers in the beauty industry.</p>
        <p class="mb-4">Through hands-on training, professional mentorship, and real-world experience, students develop valuable skills that can lead to employment opportunities or entrepreneurship.</p>
        <h3 class="text-xl font-bold mb-2">Program Highlights:</h3>
        <ul class="list-disc pl-5 mb-4">
          <li>Professional-grade equipment and supplies</li>
          <li>Instruction from licensed cosmetologists</li>
          <li>Portfolio development</li>
          <li>Business and entrepreneurship training</li>
          <li>Internship opportunities with local salons</li>
        </ul>
        <p>This program is offered in partnership with local high schools and community centers.</p>
      </div>
    </div>
  </div>
</section>

<!-- School Partnerships Program -->
<section id="partnerships" class="section">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
      <div class="order-2 md:order-1" data-aos="fade-right">
        <h2 class="text-3xl font-bold mb-4">School Partnerships</h2>
        <p class="mb-4">We collaborate with local schools to provide mentorship, resources, and support for young women, helping them navigate academic challenges and prepare for future success.</p>
        <p class="mb-4">Our school partnerships focus on building confidence, developing leadership skills, and creating a supportive community where students can thrive.</p>
        <h3 class="text-xl font-bold mb-2">Program Components:</h3>
        <ul class="list-disc pl-5 mb-4">
          <li>One-on-one mentoring</li>
          <li>After-school enrichment programs</li>
          <li>College and career readiness workshops</li>
          <li>Leadership development</li>
          <li>Academic support and tutoring</li>
        </ul>
        <p>We currently partner with schools in Pottstown, PA and surrounding areas.</p>
      </div>
      <div class="order-1 md:order-2" data-aos="fade-left">
        <img src="https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c2Nob29sJTIwY2xhc3Nyb29tfGVufDB8fDB8fHww" alt="School Partnerships" class="rounded-lg shadow-lg w-full">
      </div>
    </div>
  </div>
</section>

<!-- Community Engagement Program -->
<section id="community" class="section bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
      <div data-aos="fade-right">
        <img src="https://images.unsplash.com/photo-1529156069898-49953e39b3ac?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y29tbXVuaXR5JTIwZ3JvdXB8ZW58MHx8MHx8fDA%3D" alt="Community Engagement" class="rounded-lg shadow-lg w-full">
      </div>
      <div data-aos="fade-left">
        <h2 class="text-3xl font-bold mb-4">Community Engagement</h2>
        <p class="mb-4">Our community engagement initiatives create spaces for connection, growth, and mutual support, bringing women together to address common challenges and celebrate shared successes.</p>
        <p class="mb-4">Through workshops, events, and ongoing programs, we foster a sense of belonging and empower women to become leaders in their communities.</p>
        <h3 class="text-xl font-bold mb-2">Key Activities:</h3>
        <ul class="list-disc pl-5 mb-4">
          <li>Community workshops and seminars</li>
          <li>Support groups for women</li>
          <li>Financial literacy programs</li>
          <li>Health and wellness initiatives</li>
          <li>Community service projects</li>
        </ul>
        <p>These activities take place at our main office and various community centers throughout the year.</p>
      </div>
    </div>
  </div>
</section>

<!-- Mentorship Program -->
<section id="mentorship" class="section">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
      <div class="order-2 md:order-1" data-aos="fade-right">
        <h2 class="text-3xl font-bold mb-4">Mentorship Program</h2>
        <p class="mb-4">Our mentorship program pairs experienced professionals with women seeking guidance in their personal and professional development.</p>
        <p class="mb-4">Mentors provide support, advice, and encouragement, helping mentees navigate challenges, set goals, and develop the skills needed to achieve them.</p>
        <h3 class="text-xl font-bold mb-2">Program Benefits:</h3>
        <ul class="list-disc pl-5 mb-4">
          <li>Personalized guidance and support</li>
          <li>Professional networking opportunities</li>
          <li>Skill development workshops</li>
          <li>Career planning assistance</li>
          <li>Ongoing relationship building</li>
        </ul>
        <p>Both mentors and mentees report significant personal and professional growth through participation in this program.</p>
      </div>
      <div class="order-1 md:order-2" data-aos="fade-left">
        <img src="https://images.unsplash.com/photo-1573497620053-ea5300f94f21?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8bWVudG9yaW5nfGVufDB8fDB8fHww" alt="Mentorship Program" class="rounded-lg shadow-lg w-full">
      </div>
    </div>
  </div>
</section>

<!-- Get Involved CTA -->
<section class="section bg-primary text-white text-center">
  <div class="container mx-auto px-4">
    <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Get Involved</h2>
    <p class="max-w-2xl mx-auto mb-8" data-aos="fade-up" data-aos-delay="100">Whether you're interested in participating in our programs, volunteering your time and expertise, or supporting our work through donations, there are many ways to get involved with Sisters for Good.</p>
    <div class="flex flex-wrap justify-center gap-4" data-aos="fade-up" data-aos-delay="200">
      <a href="contact.html" class="btn btn-accent text-dark-color">Contact Us</a>
      <a href="donate.html" class="btn btn-light">Donate Now</a>
    </div>
  </div>
</section>

<!-- Footer Component -->
<footer class="bg-gray-800 text-white py-10">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Organization Info -->
      <div>
        <h3 class="text-xl font-bold mb-4">Sisters for Good</h3>
        <p class="mb-2">Empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach.</p>
        <p class="text-sm mt-4">&copy; 2025 Sisters for Good. All Rights Reserved.</p>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-xl font-bold mb-4">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="index.html" class="hover:text-pink-400 transition-colors">Home</a></li>
          <li><a href="about.html" class="hover:text-pink-400 transition-colors">About Us</a></li>
          <li><a href="programs.html" class="hover:text-pink-400 transition-colors">Programs</a></li>
          <li><a href="events.html" class="hover:text-pink-400 transition-colors">Events</a></li>
          <li><a href="donate.html" class="hover:text-pink-400 transition-colors">Donate</a></li>
          <li><a href="contact.html" class="hover:text-pink-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Information -->
      <div>
        <h3 class="text-xl font-bold mb-4">Contact Us</h3>
        <address class="not-italic">
          <p class="mb-2">📍 247 East High St, Pottstown, PA 19464</p>
          <p class="mb-2">📞 (201) 403-7417</p>
          <p class="mb-2">📧 <a href="mailto:<EMAIL>" class="hover:text-pink-400 transition-colors"><EMAIL></a></p>
        </address>

        <!-- Social Media Links -->
        <div class="mt-4 flex space-x-4">
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Facebook</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Instagram</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772a4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Twitter</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>

<!-- Chatbot Widget -->
<div id="chatbot-container" class="fixed bottom-4 right-4 w-80 bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300 z-50 chatbot-minimized">
  <div class="bg-primary text-white p-3 flex justify-between items-center cursor-pointer" id="chatbot-toggle">
    <h3 class="font-bold">Sisters for Good Assistant</h3>
    <div class="flex items-center">
      <span class="text-xl mr-2 transition-transform duration-300">▲</span>
      <span class="chatbot-close" id="chatbot-close">×</span>
    </div>
  </div>
  <div class="p-4 h-96 flex flex-col">
    <div id="chatbot-messages" class="flex-1 overflow-y-auto mb-4"></div>
    <div class="flex">
      <input type="text" id="chatbot-input" class="flex-1 border rounded-l px-3 py-2" placeholder="Ask a question...">
      <button id="chatbot-send" class="bg-primary text-white px-4 py-2 rounded-r">Send</button>
    </div>
  </div>
</div>

<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>
<script src="js/chatbot.js"></script>

<!-- Initialize Scripts -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile menu
    initMobileMenu();

    // Set active nav link
    setActiveNavLink();
  });
</script>
</body>
</html>
