<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Support Sisters for Good through donations. We accept cryptocurrency, PayPal, and traditional payment methods to help fund our programs and initiatives.">
  <title>Sisters for Good | Donate</title>

  <!-- Favicon -->
  <link rel="icon" href="favicon.ico" type="image/x-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#fc5c7d',
            secondary: '#6a82fb',
            accent: '#ffee02'
          },
          fontFamily: {
            sans: ['Poppins', 'sans-serif'],
            body: ['Roboto', 'sans-serif']
          }
        }
      }
    }
  </script>
</head>
<body>

<!-- Header Component -->
<header class="bg-white shadow-md sticky top-0 z-50">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <a href="index.html" class="flex items-center">
        <h1 class="text-xl sm:text-2xl font-bold text-pink-600">Sisters for Good</h1>
      </a>

      <!-- Desktop Navigation (hidden on mobile) -->
      <nav class="hidden md:flex space-x-6">
        <a href="index.html" class="nav-link hover:text-pink-600 transition-colors font-medium">Home</a>
        <a href="about.html" class="nav-link hover:text-pink-600 transition-colors">About Us</a>
        <a href="programs.html" class="nav-link hover:text-pink-600 transition-colors">Programs</a>
        <a href="events.html" class="nav-link hover:text-pink-600 transition-colors">Events</a>
        <a href="health.html" class="nav-link hover:text-pink-600 transition-colors">Health Resources</a>
        <a href="donate.html" class="nav-link text-pink-600 font-semibold">Donate</a>
        <a href="contact.html" class="nav-link hover:text-pink-600 transition-colors">Contact</a>
      </nav>

      <!-- Mobile menu button -->
      <button id="mobileMenuBtn" class="md:hidden flex items-center p-3 rounded-md hover:bg-gray-100 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation (hidden by default) -->
    <div id="mobileMenu" class="md:hidden hidden mt-4 pb-4 border-t border-gray-200 pt-3">
      <nav class="flex flex-col space-y-4">
        <a href="index.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Home</a>
        <a href="about.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">About Us</a>
        <a href="programs.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Programs</a>
        <a href="events.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Events</a>
        <a href="health.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Health Resources</a>
        <a href="donate.html" class="nav-link text-center py-2 bg-pink-50 text-pink-600 font-medium rounded-md">Donate</a>
        <a href="contact.html" class="nav-link text-center py-2 hover:bg-pink-50 hover:text-pink-600 transition-colors rounded-md">Contact</a>
      </nav>
    </div>
  </div>
</header>

<!-- Page Header -->
<section class="bg-gradient-to-r from-purple-700 to-blue-800 text-white py-16 relative overflow-hidden">
  <div class="absolute inset-0 bg-black opacity-10"></div>
  <div class="container mx-auto px-4 text-center relative z-10">
    <h1 class="text-4xl md:text-5xl font-bold mb-4 drop-shadow-lg" data-aos="fade-up">Support Our Mission</h1>
    <p class="text-xl max-w-3xl mx-auto leading-relaxed drop-shadow-md" data-aos="fade-up" data-aos-delay="100">Your contribution helps us continue our work empowering women and strengthening communities. Every donation makes a difference.</p>
    <div class="mt-8 flex justify-center items-center space-x-8" data-aos="fade-up" data-aos-delay="200">
      <div class="text-center">
        <div class="text-3xl font-bold text-yellow-300">💝</div>
        <div class="text-sm font-medium mt-1">Secure</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-yellow-300">🏆</div>
        <div class="text-sm font-medium mt-1">Trusted</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-yellow-300">📊</div>
        <div class="text-sm font-medium mt-1">Transparent</div>
      </div>
    </div>
  </div>
</section>

<!-- Donation Overview -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="max-w-3xl mx-auto text-center mb-12">
      <p data-aos="fade-up">Sisters for Good is a registered 501(c)(3) non-profit organization, and all donations are tax-deductible to the extent allowed by law. We offer multiple ways to donate, including cryptocurrency, PayPal, and traditional payment methods.</p>
    </div>
  </div>
</section>

<!-- Donation Options -->
<section class="section bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Ways to Donate</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Traditional Donations -->
      <div class="card" data-aos="fade-up">
        <div class="card-body">
          <h3 class="text-2xl font-bold mb-4">Traditional Donations</h3>
          <p class="mb-4">Support our work through traditional payment methods including credit card, check, or bank transfer.</p>
          <div class="mb-6">
            <h4 class="font-bold mb-2">Credit Card</h4>
            <p class="mb-4">Make a secure online donation using your credit or debit card.</p>
            <a href="#" class="btn btn-primary mb-4">Donate Now</a>
          </div>
          <div class="mb-6">
            <h4 class="font-bold mb-2">Check</h4>
            <p>Please make checks payable to "Sisters for Good" and mail to:</p>
            <address class="not-italic mt-2">
              Sisters for Good<br>
              247 East High St<br>
              Pottstown, PA 19464
            </address>
          </div>
          <div>
            <h4 class="font-bold mb-2">Bank Transfer</h4>
            <p>For bank transfer information, please contact us at <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>.</p>
          </div>
        </div>
      </div>

      <!-- PayPal Donations -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="card-body">
          <h3 class="text-2xl font-bold mb-4">PayPal Donations</h3>
          <p class="mb-4">Donate quickly and securely using PayPal. You can make a one-time donation or set up recurring monthly donations.</p>
          <div class="text-center mb-6">
            <img src="https://www.paypalobjects.com/webstatic/mktg/logo/pp_cc_mark_111x69.jpg" alt="PayPal Logo" class="mx-auto w-48 h-48 object-contain mb-4">
            <p class="text-sm mb-4">Scan the QR code with your PayPal app to donate</p>
            <a href="https://www.paypal.com/donate?hosted_button_id=YOURPAYPALID" class="btn btn-primary" target="_blank">Donate with PayPal</a>
          </div>
          <p class="text-sm">PayPal is a secure and convenient way to support our organization. You don't need a PayPal account to donate - you can use your credit or debit card through the PayPal platform.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Cryptocurrency Donations -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Cryptocurrency Donations</h2>
    </div>
    <div class="max-w-3xl mx-auto text-center mb-8">
      <p data-aos="fade-up">We accept donations in various cryptocurrencies. Your digital currency donations help fund our programs and initiatives while embracing innovative financial technologies.</p>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Bitcoin -->
      <div class="card" data-aos="fade-up">
        <div class="card-body text-center">
          <h3 class="text-xl font-bold mb-4">Bitcoin (BTC)</h3>
          <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/46/Bitcoin.svg/1200px-Bitcoin.svg.png" alt="Bitcoin Logo" class="mx-auto w-48 h-48 object-contain mb-4">
          <p class="text-sm break-all mb-4">**********************************</p>
          <button class="btn btn-primary" onclick="copyToClipboard('**********************************')">Copy Address</button>
        </div>
      </div>

      <!-- Ethereum -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="card-body text-center">
          <h3 class="text-xl font-bold mb-4">Ethereum (ETH)</h3>
          <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/Ethereum_logo_2014.svg/1257px-Ethereum_logo_2014.svg.png" alt="Ethereum Logo" class="mx-auto w-48 h-48 object-contain mb-4">
          <p class="text-sm break-all mb-4">******************************************</p>
          <button class="btn btn-primary" onclick="copyToClipboard('******************************************')">Copy Address</button>
        </div>
      </div>

      <!-- Dogecoin -->
      <div class="card" data-aos="fade-up" data-aos-delay="200">
        <div class="card-body text-center">
          <h3 class="text-xl font-bold mb-4">Dogecoin (DOGE)</h3>
          <img src="https://upload.wikimedia.org/wikipedia/en/d/d0/Dogecoin_Logo.png" alt="Dogecoin Logo" class="mx-auto w-48 h-48 object-contain mb-4">
          <p class="text-sm break-all mb-4">DS2hW5Y6Ey9AKfWXk8r4LZenPJwLnKEW4F</p>
          <button class="btn btn-primary" onclick="copyToClipboard('DS2hW5Y6Ey9AKfWXk8r4LZenPJwLnKEW4F')">Copy Address</button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Other Ways to Support -->
<section class="section bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Other Ways to Support</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Volunteer -->
      <div class="card" data-aos="fade-up">
        <div class="card-body text-center">
          <div class="text-primary text-4xl mb-4">👥</div>
          <h3 class="text-xl font-bold mb-2">Volunteer</h3>
          <p class="mb-4">Share your time and talents with our organization. We have various volunteer opportunities available.</p>
          <a href="contact.html" class="btn btn-primary">Learn More</a>
        </div>
      </div>

      <!-- In-Kind Donations -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="card-body text-center">
          <div class="text-primary text-4xl mb-4">🎁</div>
          <h3 class="text-xl font-bold mb-2">In-Kind Donations</h3>
          <p class="mb-4">Donate goods, services, or equipment that can support our programs and operations.</p>
          <a href="contact.html" class="btn btn-primary">Learn More</a>
        </div>
      </div>

      <!-- Corporate Partnerships -->
      <div class="card" data-aos="fade-up" data-aos-delay="200">
        <div class="card-body text-center">
          <div class="text-primary text-4xl mb-4">🤝</div>
          <h3 class="text-xl font-bold mb-2">Corporate Partnerships</h3>
          <p class="mb-4">Partner with us to create meaningful impact while enhancing your corporate social responsibility.</p>
          <a href="contact.html" class="btn btn-primary">Learn More</a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Impact Section -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Your Impact</h2>
    </div>
    <div class="max-w-3xl mx-auto">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center mb-8">
        <div data-aos="fade-up">
          <div class="text-4xl font-bold text-primary mb-2">200+</div>
          <p>Women supported annually</p>
        </div>
        <div data-aos="fade-up" data-aos-delay="100">
          <div class="text-4xl font-bold text-primary mb-2">50+</div>
          <p>Educational workshops</p>
        </div>
        <div data-aos="fade-up" data-aos-delay="200">
          <div class="text-4xl font-bold text-primary mb-2">10+</div>
          <p>Community partnerships</p>
        </div>
      </div>
      <p class="text-center" data-aos="fade-up">Your donations directly support our programs and initiatives, helping us create lasting positive change in the lives of women and communities. Thank you for your generosity!</p>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="section bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Donation FAQs</h2>
    </div>
    <div class="max-w-3xl mx-auto">
      <div class="mb-6" data-aos="fade-up">
        <h3 class="text-xl font-bold mb-2">Is my donation tax-deductible?</h3>
        <p>Yes, Sisters for Good is a registered 501(c)(3) non-profit organization, and all donations are tax-deductible to the extent allowed by law. You will receive a receipt for your donation that can be used for tax purposes.</p>
      </div>
      <div class="mb-6" data-aos="fade-up" data-aos-delay="100">
        <h3 class="text-xl font-bold mb-2">How is my donation used?</h3>
        <p>Your donation directly supports our programs and initiatives, including cosmetology education, school partnerships, community engagement, and mentorship programs. We strive to keep administrative costs low to maximize the impact of your contribution.</p>
      </div>
      <div class="mb-6" data-aos="fade-up" data-aos-delay="200">
        <h3 class="text-xl font-bold mb-2">Can I make a recurring donation?</h3>
        <p>Yes, you can set up recurring monthly donations through our PayPal donation option. Recurring donations provide us with a steady source of funding that helps us plan and implement our programs effectively.</p>
      </div>
      <div data-aos="fade-up" data-aos-delay="300">
        <h3 class="text-xl font-bold mb-2">How do I get a receipt for my donation?</h3>
        <p>For online donations, you will receive an automatic email receipt. For check donations, we will mail a receipt to the address provided. If you need additional documentation or have questions about your receipt, please contact us at <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>.</p>
      </div>
    </div>
  </div>
</section>

<!-- Contact CTA -->
<section class="section bg-primary text-white text-center">
  <div class="container mx-auto px-4">
    <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Questions About Donating?</h2>
    <p class="max-w-2xl mx-auto mb-8" data-aos="fade-up" data-aos-delay="100">If you have any questions about making a donation or would like to discuss other ways to support our organization, please don't hesitate to contact us.</p>
    <a href="contact.html" class="btn btn-accent text-dark-color" data-aos="fade-up" data-aos-delay="200">Contact Us</a>
  </div>
</section>

<!-- Footer Component -->
<footer class="bg-gray-800 text-white py-10">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Organization Info -->
      <div>
        <h3 class="text-xl font-bold mb-4">Sisters for Good</h3>
        <p class="mb-2">Empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach.</p>
        <p class="text-sm mt-4">&copy; 2025 Sisters for Good. All Rights Reserved.</p>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-xl font-bold mb-4">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="index.html" class="hover:text-pink-400 transition-colors">Home</a></li>
          <li><a href="about.html" class="hover:text-pink-400 transition-colors">About Us</a></li>
          <li><a href="programs.html" class="hover:text-pink-400 transition-colors">Programs</a></li>
          <li><a href="events.html" class="hover:text-pink-400 transition-colors">Events</a></li>
          <li><a href="health.html" class="hover:text-pink-400 transition-colors">Health Resources</a></li>
          <li><a href="donate.html" class="hover:text-pink-400 transition-colors">Donate</a></li>
          <li><a href="contact.html" class="hover:text-pink-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Information -->
      <div>
        <h3 class="text-xl font-bold mb-4">Contact Us</h3>
        <address class="not-italic">
          <p class="mb-2">📍 247 East High St, Pottstown, PA 19464</p>
          <p class="mb-2">📞 (201) 403-7417</p>
          <p class="mb-2">📧 <a href="mailto:<EMAIL>" class="hover:text-pink-400 transition-colors"><EMAIL></a></p>
        </address>

        <!-- Social Media Links -->
        <div class="mt-4 flex space-x-4">
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Facebook</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Instagram</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772a4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Twitter</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>

<!-- Chatbot Widget -->
<div id="chatbot-container" class="fixed bottom-4 right-4 w-80 bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300 z-50 chatbot-minimized">
  <div class="bg-primary text-white p-3 flex justify-between items-center cursor-pointer" id="chatbot-toggle">
    <h3 class="font-bold">Sisters for Good Assistant</h3>
    <div class="flex items-center">
      <span class="text-xl mr-2 transition-transform duration-300">▲</span>
      <span class="chatbot-close" id="chatbot-close">×</span>
    </div>
  </div>
  <div class="p-4 h-96 flex flex-col">
    <div id="chatbot-messages" class="flex-1 overflow-y-auto mb-4"></div>
    <div class="flex">
      <input type="text" id="chatbot-input" class="flex-1 border rounded-l px-3 py-2" placeholder="Ask a question...">
      <button id="chatbot-send" class="bg-primary text-white px-4 py-2 rounded-r">Send</button>
    </div>
  </div>
</div>

<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>
<script src="js/chatbot.js"></script>

<!-- Copy to Clipboard Function -->
<script>
  function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
      alert('Address copied to clipboard!');
    }).catch(err => {
      console.error('Could not copy text: ', err);
    });
  }
</script>

<!-- Initialize Scripts -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile menu
    initMobileMenu();

    // Set active nav link
    setActiveNavLink();
  });
</script>
</body>
</html>
